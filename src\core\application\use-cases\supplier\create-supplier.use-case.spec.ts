import { Test, TestingModule } from '@nestjs/testing';
import { CreateSupplierUseCase } from './create-supplier.use-case';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { CreateSupplierDto } from '../../../../modules/finance/dto/create-supplier.dto';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { ConflictException } from '@nestjs/common';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';

describe('CreateSupplierUseCase', () => {
  let useCase: CreateSupplierUseCase;
  let repository: jest.Mocked<SupplierRepositoryPort>;

  const mockSupplier = {
    id: 'test-id',
    name: 'Test Supplier',
    document: '12345678901234',
    tradeName: 'Test Trade',
    address: {
      street: 'Test Street',
      city: 'Test City',
      zipCode: '12345-678',
      state: 'TS',
    },
    contact: {
      email: '<EMAIL>',
      phone: '(11) 99999-9999',
    },
    type: SupplierType.BETTING, // Assuming SupplierType is an enum
    status: SupplierStatus.PENDING,
    createdAt: new Date(),
    createdBy: 'test-user',
    updatedAt: new Date(),
    updatedBy: 'test-user',
  };

  beforeEach(async () => {
    const mockRepository = {
      create: jest.fn(),
      findByDocument: jest.fn(),
      findById: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateSupplierUseCase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<CreateSupplierUseCase>(CreateSupplierUseCase);
    repository = module.get(SUPPLIER_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    const createSupplierDto: CreateSupplierDto = {
      name: 'Test Supplier',
      document: '12345678901234',
      tradeName: 'Test Trade',
      address: {
        street: 'Test Street',
        city: 'Test City',
        zipCode: '12345-678',
        state: 'TS',
      },
      contact: {
        email: '<EMAIL>',
        phone: '(11) 99999-9999',
      },
      type: SupplierType.BETTING, // Assuming SupplierType is an enum
      // status removido - será sempre PENDING por padrão
    };

    const userId = 'test-user';

    it('should create a new supplier successfully', async () => {
      repository.findByDocument.mockResolvedValue(null);
      repository.create.mockResolvedValue(mockSupplier as Supplier);

      const findBydocument = jest.spyOn(repository, 'findByDocument');
      const create = jest.spyOn(repository, 'create');

      const result = await useCase.execute(createSupplierDto, userId);

      expect(findBydocument).toHaveBeenCalledWith(createSupplierDto.document);
      expect(create).toHaveBeenCalled();
      expect(result).toEqual(mockSupplier);
    });

    it('should throw ConflictException if document already exists', async () => {
      repository.findByDocument.mockResolvedValue(mockSupplier as Supplier);

      const findBydocument = jest.spyOn(repository, 'findByDocument');
      const create = jest.spyOn(repository, 'create');

      await expect(useCase.execute(createSupplierDto, userId)).rejects.toThrow(
        ConflictException,
      );
      expect(findBydocument).toHaveBeenCalledWith(createSupplierDto.document);
      expect(create).not.toHaveBeenCalled();
    });
  });
});
