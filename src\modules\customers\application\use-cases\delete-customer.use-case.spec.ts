import { Test, TestingModule } from '@nestjs/testing';
import { DeleteCustomerUseCase } from './delete-customer.use-case';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import { NotFoundException } from '@nestjs/common';
import { CustomerStatus, CustomerType } from '../../domain/entities/customer.entity';

describe('DeleteCustomerUseCase', () => {
  let useCase: DeleteCustomerUseCase;
  let customerRepository: jest.Mocked<ICustomerRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeleteCustomerUseCase,
        {
          provide: 'ICustomerRepository',
          useValue: {
            findByUuid: jest.fn(),
            delete: jest.fn(),
          },
        },
      ],
    }).compile();

    useCase = module.get(DeleteCustomerUseCase);
    customerRepository = module.get('ICustomerRepository');
  });

  it('should delete customer if found', async () => {
    customerRepository.findByUuid.mockResolvedValue({
      uuid: '123',
      razaoSocial: 'Test',
      cnpj: 'doc',
      email: '<EMAIL>',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      type: CustomerType.OTHER,
      url: 'https://test.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    await expect(useCase.execute('123')).resolves.toBeUndefined();
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(customerRepository.delete).toHaveBeenCalledWith('123');
  });

  it('should throw NotFoundException if customer not found', async () => {
    customerRepository.findByUuid.mockResolvedValue(null);
    await expect(useCase.execute('notfound')).rejects.toThrow(
      NotFoundException,
    );
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(customerRepository.delete).not.toHaveBeenCalled();
  });
});
