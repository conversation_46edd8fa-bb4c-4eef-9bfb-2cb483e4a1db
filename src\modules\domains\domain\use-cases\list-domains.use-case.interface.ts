import { Domain } from '../entities/domain.entity';

export interface DomainFilterCriteria {
  domain?: string;
  type?: string;
}

export interface DomainListResponse {
  items: Domain[];
  limit: number;
  offset: number;
  total: number;
}

export interface IListDomainsUseCase {
  execute(
    customerUuid: string,
    criteria: DomainFilterCriteria,
    limit: number,
    offset: number,
  ): Promise<DomainListResponse>;
} 