/* eslint-disable @typescript-eslint/unbound-method */

import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeController } from './employee.controller';
import { EmployeeService } from './employee.service';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { NotFoundException } from '@nestjs/common';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { ConfigService } from '@nestjs/config';

// Mock do enum EmployeeStatus para testes
export enum EmployeeStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

describe('EmployeeController', () => {
  let controller: EmployeeController;
  let service: jest.Mocked<EmployeeService>;

  const mockWorkSchedule = {
    monday: '08:00-17:00',
    tuesday: '08:00-17:00',
    wednesday: '08:00-17:00',
    thursday: '08:00-17:00',
    friday: '08:00-17:00',
    saturday: 'OFF',
    sunday: 'OFF',
  };

  const mockVacation = {
    startDate: '2024-07-01',
    endDate: '2024-07-30',
  };

  const mockEmployeeResponse = {
    uuid: '123e4567-e89b-12d3-a456-************',
    name: 'John Doe',
    email: '<EMAIL>',
    position: 'Software Engineer',
    department: 'Engineering',
    hireDate: '2024-01-01',
    address: {
      street: 'Rua das Flores',
      number: '123',
      complement: 'Apto 101',
      neighborhood: 'Centro',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01234-567',
    },
    personalDocuments: [
      {
        type: 'CPF',
        number: '123.456.789-00',
        issuingAgency: undefined,
        issueDate: undefined,
      },
    ],
    dependents: [
      {
        name: 'Maria Silva',
        relationship: 'Filho(a)',
        birthDate: '2015-05-10',
        isTaxDependent: true,
        hasHealthPlan: true,
      },
    ],
    status: EmployeeStatus.ACTIVE,
    workSchedule: mockWorkSchedule,
    shift: ['day'],
    grossSalary: 5000.0,
    mealAllowance: 500.0,
    transportAllowance: 300.0,
    healthPlan: 'Unimed',
    contractType: ['CLT'],
    seniority: ['senior'],
    phone: '***********',
    birthDate: '1990-01-01',
    workHours: '08:00-17:00',
    overtimeBank: true,
    vacations: [mockVacation],
    workJourney: '45 horas semanais',
    createdBy: 'test-user',
    updatedBy: 'test-user',
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:00:00Z',
  };

  beforeEach(async () => {
    const mockService = {
      create: jest.fn(),
      update: jest.fn(),
      findByUuid: jest.fn(),
      findAll: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [EmployeeController],
      providers: [
        {
          provide: EmployeeService,
          useValue: mockService,
        },
        {
          provide: JwtAuthGuard,
          useValue: { canActivate: jest.fn().mockReturnValue(true) },
        },
        {
          provide: RolesGuard,
          useValue: { canActivate: jest.fn().mockReturnValue(true) },
        },
        {
          provide: ConfigService,
          useValue: { get: jest.fn() },
        },
      ],
    }).compile();

    controller = module.get<EmployeeController>(EmployeeController);
    service = module.get(EmployeeService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findByUuid', () => {
    it('should return an employee by uuid', async () => {
      const uuid = '123e4567-e89b-12d3-a456-************';
      service.findByUuid.mockResolvedValue(mockEmployeeResponse);

      const result = await controller.findByUuid(uuid);

      expect(result).toEqual(mockEmployeeResponse);
      expect(service.findByUuid).toHaveBeenCalledWith(uuid);
    });

    it('should throw NotFoundException when employee is not found', async () => {
      const uuid = 'non-existent-uuid';
      service.findByUuid.mockRejectedValue(new NotFoundException());

      await expect(controller.findByUuid(uuid)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('findAll', () => {
    it('should return a list of employees', async () => {
      const query = { limit: 10, offset: 0 };
      const paginatedResponse = {
        items: [mockEmployeeResponse],
        total: 1,
        limit: 10,
        offset: 0,
      };
      service.findAll.mockResolvedValue(paginatedResponse);

      const result = await controller.findAll(query);
      expect(result).toEqual(paginatedResponse);
      expect(service.findAll).toHaveBeenCalledWith(query);
    });
  });

  describe('create', () => {
    it('should create a new employee', async () => {
      const createDto: CreateEmployeeDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        position: 'Software Engineer',
        department: 'Engineering',
        status: EmployeeStatus.ACTIVE,
        hireDate: '2024-01-01',
        address: {
          street: 'Rua das Flores',
          number: '123',
          complement: 'Apto 101',
          neighborhood: 'Centro',
          city: 'São Paulo',
          state: 'SP',
          zipCode: '01234-567',
        },
        personalDocuments: [
          {
            type: 'CPF',
            number: '123.456.789-00',
            issuingAgency: undefined,
            issueDate: undefined,
          },
        ],
        dependents: [
          {
            name: 'Maria Silva',
            relationship: 'Filho(a)',
            birthDate: '2015-05-10',
            isTaxDependent: true,
            hasHealthPlan: true,
          },
        ],
        workSchedule: mockWorkSchedule,
        shift: ['day'],
        grossSalary: 5000.0,
        mealAllowance: 500.0,
        transportAllowance: 300.0,
        healthPlan: 'Unimed',
        contractType: ['CLT'],
        seniority: ['senior'],
        phone: '***********',
        birthDate: '1990-01-01',
        workHours: '08:00-17:00',
        overtimeBank: true,
        vacations: [mockVacation],
        createdBy: 'test-user',
        updatedBy: 'test-user',
      };

      service.create.mockResolvedValue(mockEmployeeResponse);

      const result = await controller.create(createDto);

      expect(result).toEqual(mockEmployeeResponse);
      expect(service.create).toHaveBeenCalledWith(createDto);
    });

    it('should handle empty arrays for personalDocuments and dependents', async () => {
      const dtoWithEmptyArrays = {
        ...mockEmployeeResponse,
        personalDocuments: [],
        dependents: [],
      };

      service.create.mockResolvedValue(dtoWithEmptyArrays);

      const result = await controller.create(
        dtoWithEmptyArrays as CreateEmployeeDto,
      );

      expect(result.personalDocuments).toEqual([]);
      expect(result.dependents).toEqual([]);
    });

    it('should handle optional fields', async () => {
      const dtoWithoutOptionals = {
        ...mockEmployeeResponse,
        workSchedule: undefined,
        shift: undefined,
        grossSalary: undefined,
        mealAllowance: undefined,
        transportAllowance: undefined,
        healthPlan: undefined,
        contractType: undefined,
        seniority: undefined,
        phone: undefined,
        birthDate: undefined,
        workHours: undefined,
        overtimeBank: undefined,
        vacations: undefined,
      };

      const responseWithoutOptionals = {
        ...mockEmployeeResponse,
        workSchedule: undefined,
        shift: undefined,
        grossSalary: undefined,
        mealAllowance: undefined,
        transportAllowance: undefined,
        healthPlan: undefined,
        contractType: undefined,
        seniority: undefined,
        phone: undefined,
        birthDate: undefined,
        workHours: undefined,
        overtimeBank: undefined,
        vacations: undefined,
        workJourney: undefined,
      };

      service.create.mockResolvedValue(responseWithoutOptionals);

      const result = await controller.create(
        dtoWithoutOptionals as CreateEmployeeDto,
      );

      expect(result.workSchedule).toBeUndefined();
      expect(result.shift).toBeUndefined();
      expect(result.grossSalary).toBeUndefined();
      expect(result.mealAllowance).toBeUndefined();
      expect(result.transportAllowance).toBeUndefined();
      expect(result.healthPlan).toBeUndefined();
      expect(result.contractType).toBeUndefined();
      expect(result.seniority).toBeUndefined();
      expect(result.phone).toBeUndefined();
      expect(result.birthDate).toBeUndefined();
      expect(result.workHours).toBeUndefined();
      expect(result.overtimeBank).toBeUndefined();
      expect(result.vacations).toBeUndefined();
      expect(result.workJourney).toBeUndefined();
    });
  });

  describe('update', () => {
    it('should update an employee', async () => {
      const uuid = '123e4567-e89b-12d3-a456-************';
      const updateDto = {
        name: 'Updated Name',
        address: {
          street: 'Rua das Flores',
          number: '123',
          complement: 'Apto 101',
          neighborhood: 'Centro',
          city: 'São Paulo',
          state: 'SP',
          zipCode: '01234-567',
        },
        updatedBy: 'test-user',
      };
      service.update.mockResolvedValue(mockEmployeeResponse);

      const result = await controller.update(uuid, updateDto);
      expect(result).toEqual(mockEmployeeResponse);
      expect(service.update).toHaveBeenCalledWith(uuid, updateDto);
    });
  });

  describe('delete', () => {
    it('should delete an employee', async () => {
      const uuid = '123e4567-e89b-12d3-a456-************';
      service.delete.mockResolvedValue(undefined);

      await expect(controller.delete(uuid)).resolves.toBeUndefined();
      expect(service.delete).toHaveBeenCalledWith(uuid);
    });
  });
});
