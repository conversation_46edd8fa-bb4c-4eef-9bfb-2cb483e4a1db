import { Test, TestingModule } from '@nestjs/testing';
import { UpdateSupplierUseCase } from './update-supplier.use-case';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { Contact } from '../../../domain/supplier/value-objects/contact.value-object';
import { SupplierStatus } from '../../../domain/supplier/enums/supplier-status.enum';
import { NotFoundException, ConflictException } from '@nestjs/common';
import { SupplierType } from '../../../domain/supplier/enums/supplier-type.enum';

describe('UpdateSupplierUseCase', () => {
  let useCase: UpdateSupplierUseCase;
  let repository: jest.Mocked<SupplierRepositoryPort>;

  const mockSupplier = new Supplier(
    'test-id',
    'Test Supplier',
    '12345678901234',
    'Test Trade',
    new Address('Test Street', 'Test City', '12345-678', 'SP'),
    new Contact('<EMAIL>', '(11) 99999-9999'),
    SupplierType.BETTING, // Assuming SupplierType is an enum
    SupplierStatus.ACTIVE,
    'user-id',
    new Date(),
    new Date(),
    'user-id',
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<SupplierRepositoryPort> = {
      findById: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByDocument: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateSupplierUseCase,
        {
          provide: SUPPLIER_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<UpdateSupplierUseCase>(UpdateSupplierUseCase);
    repository =
      module.get<jest.Mocked<SupplierRepositoryPort>>(SUPPLIER_REPOSITORY);
  });

  describe('execute', () => {
    it('should update supplier when user is authorized', async () => {
      // Arrange
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation((supplier) =>
        Promise.resolve(supplier),
      );

      const update = jest.spyOn(repository, 'update');

      // Act
      const result = await useCase.execute(
        'test-id',
        { tradeName: 'New Trade Name' },
        'user-id',
      );

      // Assert
      expect(result.tradeName).toBe('New Trade Name');
      expect(update).toHaveBeenCalledTimes(1);
    });

    it('should throw NotFoundException when supplier does not exist', async () => {
      // Arrange
      repository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(
        useCase.execute('non-existent-id', {}, 'user-id'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should update all supplier fields successfully', async () => {
      // Arrange
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation((supplier) =>
        Promise.resolve(supplier),
      );

      const updateData = {
        name: 'New Name',
        cnpj: '98765432109876',
        tradeName: 'New Trade Name',
        address: {
          street: 'New Street',
          city: 'New City',
          zipCode: '98765-432',
          state: 'RJ',
        },
        contact: {
          email: '<EMAIL>',
          phone: '(21) 88888-8888',
        },
        status: SupplierStatus.INACTIVE,
      };

      // Act
      const result = await useCase.execute('test-id', updateData, 'user-id');

      // Assert
      expect(result.name).toBe(updateData.name);
      expect(result.document).toBe(updateData.cnpj);
      expect(result.tradeName).toBe(updateData.tradeName);
      expect(result.address.street).toBe(updateData.address.street);
      expect(result.address.city).toBe(updateData.address.city);
      expect(result.address.zipCode).toBe(updateData.address.zipCode);
      expect(result.address.state).toBe(updateData.address.state);
      expect(result.contact.email).toBe(updateData.contact.email);
      expect(result.contact.phone).toBe(updateData.contact.phone);
      expect(result.status).toBe(updateData.status);
    });

    it('should throw ConflictException when CNPJ already exists', async () => {
      // Arrange
      const existingSupplier = new Supplier(
        'other-id',
        'Other Supplier',
        '12345678901234',
        'Other Trade',
        new Address('Other Street', 'Other City', '54321-876', 'RJ'),
        new Contact('<EMAIL>', '(21) 77777-7777'),
        SupplierType.BETTING,
        SupplierStatus.ACTIVE,
        'user-id',
        new Date(),
        new Date(),
        'user-id',
      );

      repository.findById.mockResolvedValue(mockSupplier);
      repository.findByDocument.mockResolvedValue(existingSupplier);

      // Act & Assert
      await expect(
        useCase.execute('test-id', { cnpj: '12345678901234' }, 'user-id'),
      ).rejects.toThrow(ConflictException);
    });

    it('should update only address when only address is provided', async () => {
      // Arrange
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation((supplier) =>
        Promise.resolve(supplier),
      );

      const updateData = {
        address: {
          street: 'New Street',
          city: 'New City',
          zipCode: '98765-432',
          state: 'RJ',
        },
      };

      // Act
      const result = await useCase.execute('test-id', updateData, 'user-id');

      // Assert
      expect(result.address.street).toBe(updateData.address.street);
      expect(result.address.city).toBe(updateData.address.city);
      expect(result.address.zipCode).toBe(updateData.address.zipCode);
      expect(result.address.state).toBe(updateData.address.state);
      expect(result.name).toBe(mockSupplier.name);
      expect(result.document).toBe(mockSupplier.document);
      expect(result.tradeName).toBe(mockSupplier.tradeName);
    });

    it('should update only contact when only contact is provided', async () => {
      // Arrange
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation((supplier) =>
        Promise.resolve(supplier),
      );

      const updateData = {
        contact: {
          email: '<EMAIL>',
          phone: '(21) 88888-8888',
        },
      };

      // Act
      const result = await useCase.execute('test-id', updateData, 'user-id');

      // Assert
      expect(result.contact.email).toBe(updateData.contact.email);
      expect(result.contact.phone).toBe(updateData.contact.phone);
      expect(result.name).toBe(mockSupplier.name);
      expect(result.document).toBe(mockSupplier.document);
      expect(result.tradeName).toBe(mockSupplier.tradeName);
    });

    it('should update only status when only status is provided', async () => {
      // Arrange
      repository.findById.mockResolvedValue(mockSupplier);
      repository.update.mockImplementation((supplier) =>
        Promise.resolve(supplier),
      );

      const updateData = {
        status: SupplierStatus.INACTIVE,
      };

      // Act
      const result = await useCase.execute('test-id', updateData, 'user-id');

      // Assert
      expect(result.status).toBe(updateData.status);
      expect(result.name).toBe(mockSupplier.name);
      expect(result.document).toBe(mockSupplier.document);
      expect(result.tradeName).toBe(mockSupplier.tradeName);
    });
  });
});
