import { ApiProperty } from '@nestjs/swagger';
import { Domain } from '../../domain/entities/domain.entity';
import { DomainType } from '@prisma/client';

export class DomainResponseDto {
  @ApiProperty({ description: 'UUID do domínio', format: 'uuid' })
  uuid: string;

  @ApiProperty({ description: 'Nome do domínio' })
  domain: string;

  @ApiProperty({ description: 'Tipo de domínio', enum: DomainType })
  type: DomainType;

  @ApiProperty({ description: 'Data de criação', format: 'date-time' })
  createdAt: Date;

  @ApiProperty({ description: 'Data de atualização', format: 'date-time' })
  updatedAt: Date;

  @ApiProperty({ description: 'Data de exclusão (soft delete)', format: 'date-time', nullable: true })
  deletedAt: Date | null;

  static fromEntity(domain: Domain): DomainResponseDto {
    const dto = new DomainResponseDto();
    dto.uuid = domain.uuid;
    dto.domain = domain.domain;
    dto.type = domain.type;
    dto.createdAt = domain.createdAt;
    dto.updatedAt = domain.updatedAt;
    dto.deletedAt = domain.deletedAt;
    return dto;
  }
} 