{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.name }}-ingress
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    app: {{ .Values.name }}-ingress
    helm.sh/chart: "{{.Chart.Name}}-{{.Chart.Version}}"
spec:
  rules:
  {{- range .Values.ingress.hosts }}
  - host: {{ .name }}
    http:
      paths:
      - backend:
          service:
            name: {{ $.Values.name }}-svc
            port:
              number: {{ $.Values.service.targetPort }}
        path: {{ .path }}
        pathType: Prefix    
  {{- end }}
    {{- range $name, $value := .Values.ingress.multipathsdep }}
      {{- if not (empty $value) }}
      - path: {{ $name | quote }}
        backend:
          service:
            name: {{ $value | quote}}
            port: {{ $.Values.ingress.multipathsportdep }}
      {{- end }}
    {{- end }}
{{- end }}