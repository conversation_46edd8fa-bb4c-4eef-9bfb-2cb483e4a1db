import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { User } from '../../core/domain/user.entity';
import { UserRepository } from '../../core/ports/repositories/user-repository.interface';
import { Role } from '../../core/domain/role.enum';
import type {
  Role as PrismaRole,
  Prisma,
  User as PrismaUser,
} from '@prisma/client';

@Injectable()
export class PrismaUserRepository implements UserRepository {
  constructor(private prisma: PrismaService) {}

  async findAll(): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      where: { deletedAt: null },
    });
    return users.map((user) => this.mapPrismaUserToDomain(user));
  }

  async findById(id: string): Promise<User | null> {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });
    if (!user || user.deletedAt) return null;
    return this.mapPrismaUserToDomain(user);
  }

  async findByKeycloakId(keycloakId: string): Promise<User> {
    const user = await this.prisma.user.findFirst({
      where: { keycloakId: keycloakId },
    });
    if (!user) {
      throw new NotFoundException(
        `User with Keycloak ID ${keycloakId} not found`,
      );
    }
  
    return this.mapPrismaUserToDomain(user);
  }

  async findByEmail(email: string): Promise<User | null> {
    const user = await this.prisma.user.findUnique({
      where: { email },
    });
    if (!user || user.deletedAt) return null;
    return this.mapPrismaUserToDomain(user);
  }

  async create(user: User): Promise<User> {
    const createdUser = await this.prisma.user.create({
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        password: user.password,
        role: user.role as PrismaRole,
        keycloakId: user.keycloakId,
      } as Prisma.UserCreateInput,
    });

    return this.mapPrismaUserToDomain(createdUser);
  }

  async update(user: User): Promise<User> {
    const updatedUser = await this.prisma.user.update({
      where: { id: user.id },
      data: {
        name: user.name,
        password: user.password,
        role: user.role as PrismaRole,
        keycloakId: user.keycloakId,
      },
    });

    return this.mapPrismaUserToDomain(updatedUser);
  }

  async updateUserKeycloakId(
    userId: string,
    keycloakId: string,
  ): Promise<User> {
    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: {
        keycloakId: keycloakId,
      },
    });

    return this.mapPrismaUserToDomain(updatedUser);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.user.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }

  private mapPrismaUserToDomain(prismaUser: PrismaUser): User {
    const keycloakId =
      prismaUser.keycloakId !== null && prismaUser.keycloakId !== undefined
        ? String(prismaUser.keycloakId)
        : undefined;

    return new User(
      prismaUser.id,
      prismaUser.email,
      prismaUser.name || '',
      prismaUser.password,
      prismaUser.role as Role,
      prismaUser.createdAt,
      prismaUser.updatedAt,
      keycloakId,
    );
  }
}
