import { ApiProperty } from '@nestjs/swagger';
import {
  BankAccountType,
  BankPixKeyType,
  BankDataStatus,
} from '@prisma/client';

export class BankDataResponseDto {
  @ApiProperty({
    description: 'ID único dos dados bancários',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  id: string;

  @ApiProperty({
    description: 'Nome do banco',
    example: 'Banco do Brasil S.A.',
  })
  bankName: string;

  @ApiProperty({
    description: 'Código do banco',
    example: '001',
  })
  bankCode: string;

  @ApiProperty({
    description: 'Tipo da conta',
    enum: BankAccountType,
    example: BankAccountType.CHECKING,
  })
  accountType: BankAccountType;

  @ApiProperty({
    description: 'Número da agência',
    example: '1234',
  })
  agencyNumber: string;

  @ApiProperty({
    description: 'Dígito da agência',
    example: '5',
    required: false,
  })
  agencyDigit?: string | null;

  @ApiProperty({
    description: 'Nú<PERSON>o da conta',
    example: '123456',
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Dígito da conta',
    example: '7',
  })
  accountDigit: string;

  @ApiProperty({
    description: 'Nome do titular da conta',
    example: 'João da Silva',
  })
  accountHolderName: string;

  @ApiProperty({
    description: 'Documento do titular da conta',
    example: '***********',
  })
  accountHolderDocument: string;

  @ApiProperty({
    description: 'Chave PIX',
    example: '<EMAIL>',
    required: false,
  })
  pixKey?: string | null;

  @ApiProperty({
    description: 'Tipo da chave PIX',
    enum: BankPixKeyType,
    example: BankPixKeyType.EMAIL,
    required: false,
  })
  pixKeyType?: BankPixKeyType | null;

  @ApiProperty({
    description: 'Indica se é um banco digital',
    example: false,
  })
  isDigitalBank?: boolean | null;

  @ApiProperty({
    description: 'Status dos dados bancários',
    enum: BankDataStatus,
    example: BankDataStatus.ACTIVE,
  })
  status?: BankDataStatus | null;

  @ApiProperty({
    description: 'ID do funcionário',
    example: 1,
  })
  employeeId?: number;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}
