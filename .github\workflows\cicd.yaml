name: CI/CD - Pull Request (NestJS)

on:
  pull_request:
    types: [opened, synchronize, reopened]

env:
  # AWS Configuration
  AWS_REGION: us-east-2
  EKS_CLUSTER_NAME_HML: hml
  EKS_CLUSTER_NAME_PRD: prd

  # Database Configuration
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  POSTGRES_DB: backoffice_test
  POSTGRES_HOST: localhost
  POSTGRES_PORT: 5432

  # RabbitMQ Configuration
  RABBITMQ_DEFAULT_USER: guest
  RABBITMQ_DEFAULT_PASS: guest
  RABBITMQ_PORT: 5672

  # Application Configuration
  JWT_SECRET: test_secret
  JWT_EXPIRATION: "3600"
  OTEL_EXPORTER_OTLP_ENDPOINT: http://localhost:4318

jobs:
  # start-postgres:
  #   name: Start PostgreSQL Container
  #   runs-on: ubuntu-latest
  #   outputs:
  #     connection_uri: ${{ steps.postgres.outputs.connection_uri }}
  #     container_id:   ${{ steps.postgres.outputs.container_id }}
  #   steps:
  #     - uses: actions/checkout@v4

  #     - name: Install Docker
  #       uses: docker/setup-buildx-action@v3

  #     - name: Install TestContainers
  #       run: npm install --save-dev @testcontainers/postgresql testcontainers

  #     - name: Start PostgreSQL
  #       id: postgres
  #       env:
  #         POSTGRES_USER:     ${{ env.POSTGRES_USER }}
  #         POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
  #         POSTGRES_DB:       ${{ env.POSTGRES_DB }}
  #       run: |
  #         node -e "
  #         const { PostgreSqlContainer } = require('@testcontainers/postgresql');
  #         (async () => {
  #           const container = await new PostgreSqlContainer()
  #             .withDatabase(process.env.POSTGRES_DB)
  #             .withUsername(process.env.POSTGRES_USER)
  #             .withPassword(process.env.POSTGRES_PASSWORD)
  #             .withExposedPorts(process.env.POSTGRES_PORT)
  #             .start();
  #           console.log('::set-output name=connection_uri::' + container.getConnectionUri());
  #           console.log('::set-output name=container_id::'   + container.getId());
  #         })().catch(console.error);
  #         "

  # start-rabbitmq:
  #   name: Start RabbitMQ Container
  #   runs-on: ubuntu-latest
  #   outputs:
  #     connection_uri: ${{ steps.rabbitmq.outputs.connection_uri }}
  #     container_id:   ${{ steps.rabbitmq.outputs.container_id }}
  #     mapped_port:    ${{ steps.rabbitmq.outputs.mapped_port }}
  #   steps:
  #     - uses: actions/checkout@v4

  #     - name: Install Docker
  #       uses: docker/setup-buildx-action@v3

  #     - name: Install TestContainers
  #       run: npm install --save-dev @testcontainers/rabbitmq testcontainers

  #     - name: Start RabbitMQ
  #       id: rabbitmq
  #       env:
  #         RABBITMQ_DEFAULT_USER: ${{ env.RABBITMQ_DEFAULT_USER }}
  #         RABBITMQ_DEFAULT_PASS: ${{ env.RABBITMQ_DEFAULT_PASS }}
  #       run: |
  #         node -e "
  #         const { RabbitMQContainer } = require('@testcontainers/rabbitmq');
  #         (async () => {
  #           const container = await new RabbitMQContainer()
  #             .withUsername(process.env.RABBITMQ_DEFAULT_USER)
  #             .withPassword(process.env.RABBITMQ_DEFAULT_PASS)
  #             .withExposedPorts(process.env.RABBITMQ_PORT)
  #             .start();
  #           console.log('::set-output name=connection_uri::' + container.getConnectionUri());
  #           console.log('::set-output name=container_id::'   + container.getId());
  #           console.log('::set-output name=mapped_port::'    + container.getMappedPort(process.env.RABBITMQ_PORT));
  #         })().catch(console.error);
  #         "

  # lint:
  #   name: Lint Code
  #   runs-on: ubuntu-latest
  #   strategy:
  #     matrix:
  #       node-version: [20.x, 22.x]
  #   steps:
  #     - uses: actions/checkout@v4

  #     - uses: actions/setup-node@v4
  #       with:
  #         node-version: ${{ matrix.node-version }}
  #         cache: 'npm'

  #     - run: npm ci
  #     - run: npm run lint

  #---------------------------------------------
  # Se precisar reativar os testes, descomente:
  #
  # test:
  #   name: Run Tests
  #   runs-on: ubuntu-latest
  #   needs: lint
  #   strategy:
  #     matrix:
  #       node-version: [20.x, 22.x]
  #   steps:
  #     - uses: actions/checkout@v4
  #
  #     - uses: actions/setup-node@v4
  #       with:
  #         node-version: ${{ matrix.node-version }}
  #         cache: 'npm'
  #
  #     - name: Install Docker
  #       uses: docker/setup-buildx-action@v3
  #
  #     - name: Install TestContainers
  #       run: |
  #         npm install --save-dev @testcontainers/postgresql @testcontainers/rabbitmq testcontainers
  #
  #     - name: Run Tests with Containers
  #       env:
  #         POSTGRES_USER:           ${{ env.POSTGRES_USER }}
  #         POSTGRES_PASSWORD:       ${{ env.POSTGRES_PASSWORD }}
  #         POSTGRES_DB:             ${{ env.POSTGRES_DB }}
  #         RABBITMQ_DEFAULT_USER:   ${{ env.RABBITMQ_DEFAULT_USER }}
  #         RABBITMQ_DEFAULT_PASS:   ${{ env.RABBITMQ_DEFAULT_PASS }}
  #         NODE_ENV:                test
  #         JWT_SECRET:              test_secret
  #         JWT_EXPIRATION:          "3600"
  #         OTEL_EXPORTER_OTLP_ENDPOINT: http://localhost:4318
  #       run: |
  #         node -e "
  #         const { PostgreSqlContainer } = require('@testcontainers/postgresql');
  #         const { RabbitMQContainer } = require('@testcontainers/rabbitmq');
  #         (async () => {
  #           const postgresContainer = await new PostgreSqlContainer()
  #             .withDatabase(process.env.POSTGRES_DB)
  #             .withUsername(process.env.POSTGRES_USER)
  #             .withPassword(process.env.POSTGRES_PASSWORD)
  #             .withExposedPorts(process.env.POSTGRES_PORT)
  #             .start();
  #           const rabbitmqContainer = await new RabbitMQContainer()
  #             .withUsername(process.env.RABBITMQ_DEFAULT_USER)
  #             .withPassword(process.env.RABBITMQ_DEFAULT_PASS)
  #             .withExposedPorts(process.env.RABBITMQ_PORT)
  #             .start();
  #           process.env.DATABASE_URL = postgresContainer.getConnectionUri();
  #           process.env.RABBITMQ_URL   = rabbitmqContainer.getConnectionUri();
  #           process.env.RABBITMQ_PORT  = rabbitmqContainer.getMappedPort(process.env.RABBITMQ_PORT);
  #           const { execSync } = require('child_process');
  #           execSync('npm test', { stdio: 'inherit' });
  #           await postgresContainer.stop();
  #           await rabbitmqContainer.stop();
  #         })().catch(console.error);
  #         "
  #---------------------------------------------

  build:
    name: Build NestJS
    # needs: lint
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20.x, 22.x]
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - run: npm ci
      - run: npm run build

  # stop-containers:
  #   name: Stop Test Containers
  #   needs: [start-postgres, start-rabbitmq, build]
  #   runs-on: ubuntu-latest
  #   if: always()
  #   steps:
  #     - uses: actions/checkout@v4

  #     - name: Install Docker
  #       uses: docker/setup-buildx-action@v3

  #     - name: Install TestContainers
  #       run: npm install --save-dev testcontainers

  #     - name: Stop Containers
  #       run: |
  #         node -e "
  #         const { GenericContainer } = require('testcontainers');
  #         (async () => {
  #           await GenericContainer.fromContainerId('${{ needs.start-postgres.outputs.container_id }}').stop();
  #           await GenericContainer.fromContainerId('${{ needs.start-rabbitmq.outputs.container_id }}').stop();
  #           console.log('Containers stopped successfully!');
  #         })().catch(console.error);
  #         "

  homologation:
    name: Deploy para Homologação
    # needs: [build, stop-containers]
    needs: [build]
    if: ${{ github.event.pull_request.labels && contains(join(github.event.pull_request.labels.*.name, ','), 'ci-hml') }}
    runs-on: ubuntu-latest
    steps:
      - name: Set short git commit SHA
        id: commit
        uses: prompt/actions-commit-hash@v2

      - name: Checkout do repositório
        uses: actions/checkout@v3

      - name: Instalar kubectl
        uses: azure/setup-kubectl@v2.0
        with:
          version: "v1.24.0"

      - name: Configurar credenciais AWS
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id:     ${{ secrets.AWS_ACCESS_KEY_ID_CI }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_CI }}
          aws-region:            ${{ env.AWS_REGION }}

      - name: Login no ECR
        run: |
          aws ecr get-login-password --region ${{ env.AWS_REGION }} \
            | docker login --username AWS --password-stdin 038462752057.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com

      - name: Build & Push da imagem
        env:
          IMAGE_TAG: ${{ steps.commit.outputs.short }}
        run: |
          docker build -t backoffice-backend-service:$IMAGE_TAG --target production .
          docker tag backoffice-backend-service:$IMAGE_TAG 038462752057.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/backoffice-backend-service:$IMAGE_TAG
          docker push 038462752057.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/backoffice-backend-service:$IMAGE_TAG

      - name: Configurar cliente Kubernetes
        uses: silverlyra/setup-aws-eks@v0.1.1
        with:
          cluster: ${{ env.EKS_CLUSTER_NAME_HML }}

      - name: Instalar Helm
        run: curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

      - name: Deploy com Helm
        run: |
          cd helm/hml/petrus-backoffice-backend-service-hml/
          sed -i.bak "s|latest|${{ steps.commit.outputs.short }}|g" values.yaml
          helm upgrade petrus-backoffice-backend-service-hml . -f values.yaml -n petrus-hml -i --force
