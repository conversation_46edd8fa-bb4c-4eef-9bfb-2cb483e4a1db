import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsEmail,
  IsUUID,
  IsObject,
  IsArray,
  ValidateNested,
  IsOptional,
  IsEnum,
  MaxLength,
  Matches,
  IsNumber,
  IsBoolean,
  IsDateString,
  ArrayMinSize,
} from 'class-validator';
import { Type } from 'class-transformer';
import { EmployeeStatus } from '@prisma/client';
import { WorkScheduleDto, VacationDto } from './create-employee.dto';

export class AddressDto {
  @ApiProperty({
    description: '<PERSON>ua',
    example: 'Avenida Paulista',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Rua deve ser uma string' })
  @MaxLength(255, { message: 'Rua deve ter no máximo 255 caracteres' })
  street?: string;

  @ApiProperty({
    description: 'Número',
    example: '1000',
    maxLength: 20,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Número deve ser uma string' })
  @MaxLength(20, { message: 'Número deve ter no máximo 20 caracteres' })
  number?: string;

  @ApiProperty({
    description: 'Complemento',
    example: 'Sala 123',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Complemento deve ser uma string' })
  @MaxLength(255, { message: 'Complemento deve ter no máximo 255 caracteres' })
  complement?: string;

  @ApiProperty({
    description: 'Bairro',
    example: 'Bela Vista',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Bairro deve ser uma string' })
  @MaxLength(255, { message: 'Bairro deve ter no máximo 255 caracteres' })
  neighborhood?: string;

  @ApiProperty({
    description: 'Cidade',
    example: 'São Paulo',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Cidade deve ser uma string' })
  @MaxLength(255, { message: 'Cidade deve ter no máximo 255 caracteres' })
  city?: string;

  @ApiProperty({
    description: 'Estado',
    example: 'SP',
    maxLength: 2,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Estado deve ser uma string' })
  @MaxLength(2, { message: 'Estado deve ter no máximo 2 caracteres' })
  state?: string;

  @ApiProperty({
    description: 'CEP',
    example: '01310100',
    maxLength: 8,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'CEP deve ser uma string' })
      @MaxLength(9, { message: 'CEP deve ter no máximo 9 caracteres' })
  @Matches(/^\d{5}-?\d{3}$/, { message: 'CEP deve estar no formato 00000-000 ou 00000000' })
  zipCode?: string;
}

export class PersonalDocumentDto {
  @ApiProperty({
    description: 'Tipo de documento (e.g., CPF, RG)',
    example: 'CPF',
    maxLength: 50,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O tipo de documento deve ser uma string' })
  @MaxLength(50, {
    message: 'O tipo de documento deve ter no máximo 50 caracteres',
  })
  type?: string;

  @ApiProperty({
    description: 'Número do documento',
    example: '12345678900',
    maxLength: 50,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O número do documento deve ser uma string' })
  @MaxLength(50, {
    message: 'O número do documento deve ter no máximo 50 caracteres',
  })
  number?: string;

  @ApiProperty({
    description: 'Órgão emissor',
    example: 'SSP',
    maxLength: 50,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O órgão emissor deve ser uma string' })
  @MaxLength(50, {
    message: 'O órgão emissor deve ter no máximo 50 caracteres',
  })
  issuingAgency?: string;

  @ApiProperty({
    description: 'Data de emissão',
    example: '2024-01-01',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'A data de emissão deve ser uma string' })
  issueDate?: string;
}

export class DependentDto {
  @ApiProperty({
    description: 'Nome do dependente',
    example: 'João Silva',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O nome do dependente deve ser uma string' })
  @MaxLength(255, {
    message: 'O nome do dependente deve ter no máximo 255 caracteres',
  })
  name?: string;

  @ApiProperty({
    description: 'Relação com o colaborador',
    example: 'Filho',
    maxLength: 50,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'A relação deve ser uma string' })
  @MaxLength(50, { message: 'A relação deve ter no máximo 50 caracteres' })
  relationship?: string;

  @ApiProperty({
    description: 'Data de nascimento',
    example: '2000-01-01',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'A data de nascimento deve ser uma string' })
  birthDate?: string;
}

export class UpdateEmployeeDto {
  @ApiProperty({
    description: 'Nome do colaborador',
    example: 'João Silva',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O nome deve ser uma string' })
  @MaxLength(255, { message: 'O nome deve ter no máximo 255 caracteres' })
  name?: string;

  @ApiProperty({
    description: 'Email do colaborador',
    example: '<EMAIL>',
    maxLength: 320,
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email inválido' })
  @MaxLength(320, { message: 'O email deve ter no máximo 320 caracteres' })
  email?: string;

  @ApiProperty({
    description: 'Cargo do colaborador',
    example: 'Desenvolvedor Senior',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O cargo deve ser uma string' })
  @MaxLength(100, { message: 'O cargo deve ter no máximo 100 caracteres' })
  position?: string;

  @ApiProperty({
    description: 'Departamento do colaborador',
    example: 'Tecnologia',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'O departamento deve ser uma string' })
  @MaxLength(100, {
    message: 'O departamento deve ter no máximo 100 caracteres',
  })
  department?: string;

  @ApiProperty({
    description: 'Data de contratação do colaborador',
    example: '2024-01-01',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'A data de contratação deve ser uma string' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'A data deve estar no formato YYYY-MM-DD',
  })
  hireDate?: string;

  @ApiProperty({
    description: 'Endereço do colaborador',
    type: AddressDto,
    example: {
      street: 'Rua das Flores',
      number: '123',
      complement: 'Apto 45',
      neighborhood: 'Jardim das Flores',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01311000',
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Endereço deve ser um objeto' })
  @ValidateNested({ message: 'Endereço contém campos inválidos' })
  @Type(() => AddressDto)
  address: AddressDto;

  @ApiProperty({
    description: 'Documentos pessoais',
    type: [PersonalDocumentDto],
    example: [
      {
        type: 'CPF',
        number: '12345678900',
        issuingAgency: 'SSP',
        issueDate: '2024-01-01',
      },
    ],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Os documentos pessoais devem ser um array' })
  @ValidateNested({
    each: true,
    message: 'Os documentos pessoais contêm campos inválidos',
  })
  @Type(() => PersonalDocumentDto)
  personalDocuments?: PersonalDocumentDto[];

  @ApiProperty({
    description: 'Dependentes',
    type: [DependentDto],
    example: [
      {
        name: 'Maria Silva',
        relationship: 'Filha',
        birthDate: '2010-01-01',
      },
    ],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Os dependentes devem ser um array' })
  @ValidateNested({
    each: true,
    message: 'Os dependentes contêm campos inválidos',
  })
  @Type(() => DependentDto)
  dependents?: DependentDto[];

  @ApiProperty({
    description: 'Status do colaborador',
    enum: EmployeeStatus,
    example: EmployeeStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(EmployeeStatus, { message: 'Status inválido' })
  status?: EmployeeStatus;

  @ApiProperty({
    description: 'UUID do usuário que atualizou o colaborador',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty({ message: 'O UUID do atualizador é obrigatório' })
  @IsUUID('4', { message: 'UUID inválido' })
  updatedBy: string;

  @ApiProperty({
    description: 'Escala de trabalho',
    type: WorkScheduleDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => WorkScheduleDto)
  workSchedule?: WorkScheduleDto;

  @ApiProperty({
    description: 'Turno de trabalho',
    example: ['day', 'night'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  shift?: string[];

  @ApiProperty({
    description: 'Salário bruto',
    example: 5000.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  grossSalary?: number;

  @ApiProperty({
    description: 'Vale refeição',
    example: 500.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  mealAllowance?: number;

  @ApiProperty({
    description: 'Vale transporte',
    example: 300.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  transportAllowance?: number;

  @ApiProperty({
    description: 'Plano de saúde',
    example: 'Unimed',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  healthPlan?: string;

  @ApiProperty({
    description: 'Tipo de contrato',
    example: ['CLT', 'PJ'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  contractType?: string[];

  @ApiProperty({
    description: 'Senioridade',
    example: ['junior', 'pleno', 'senior'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  seniority?: string[];

  @ApiProperty({
    description: 'Telefone',
    example: '11999999999',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  phone?: string;

  @ApiProperty({
    description: 'Data de nascimento',
    example: '1990-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  birthDate?: string;

  @ApiProperty({
    description: 'Horário de trabalho',
    example: '08:00-17:00',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  workHours?: string;

  @ApiProperty({
    description: 'Banco de horas',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  overtimeBank?: boolean;

  @ApiProperty({
    description: 'Férias',
    type: [VacationDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => VacationDto)
  vacations?: VacationDto[];
}
