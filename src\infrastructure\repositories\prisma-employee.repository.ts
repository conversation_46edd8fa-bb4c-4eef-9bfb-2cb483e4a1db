import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EmployeeRepositoryPort } from '@/core/ports/repositories/employee-repository.port';
import {
  Employee,
  Address,
  PersonalDocument,
  Dependent,
  WorkSchedule,
  Vacation,
} from '@/core/domain/entities/employee.entity';
import { Prisma, Employee as PrismaEmployee } from '@prisma/client';

type PrismaEmployeeWithRelations = PrismaEmployee & {
  address: Address;
  personalDocuments: PersonalDocument[];
  dependents: Dependent[];
  position: string;
  department: string;
  hireDate: Date;
  workSchedule?: WorkSchedule;
  shift?: string[];
  grossSalary?: number;
  mealAllowance?: number;
  transportAllowance?: number;
  healthPlan?: string;
  contractType?: string[];
  seniority?: string[];
  phone?: string;
  birthDate?: Date;
  workHours?: string;
  overtimeBank?: boolean;
  vacations?: Vacation[];
};

@Injectable()
export class PrismaEmployeeRepository implements EmployeeRepositoryPort {
  constructor(private readonly prisma: PrismaService) {}

  private toDomain(prismaEmployee: PrismaEmployeeWithRelations): Employee {
    return Employee.create(
      prismaEmployee.id,
      prismaEmployee.uuid,
      prismaEmployee.name,
      prismaEmployee.email,
      prismaEmployee.position,
      prismaEmployee.department,
      prismaEmployee.hireDate,
      prismaEmployee.address,
      prismaEmployee.personalDocuments,
      prismaEmployee.dependents,
      prismaEmployee.status,
      prismaEmployee.createdBy,
      prismaEmployee.updatedBy,
      prismaEmployee.createdAt,
      prismaEmployee.updatedAt,
      prismaEmployee.workSchedule,
      prismaEmployee.shift,
      prismaEmployee.grossSalary,
      prismaEmployee.mealAllowance,
      prismaEmployee.transportAllowance,
      prismaEmployee.healthPlan,
      prismaEmployee.contractType,
      prismaEmployee.seniority,
      prismaEmployee.phone,
      prismaEmployee.birthDate,
      prismaEmployee.workHours,
      prismaEmployee.overtimeBank,
      prismaEmployee.vacations,
    );
  }

  private toPrisma(employee: Employee): Prisma.EmployeeCreateInput {
    return {
      uuid: employee.uuid,
      name: employee.name,
      email: employee.email,
      position: employee.position,
      department: employee.department,
      hireDate: new Date(employee.hireDate),
      address: JSON.parse(
        JSON.stringify(employee.address),
      ) as Prisma.InputJsonValue,
      personalDocuments: JSON.parse(
        JSON.stringify(employee.personalDocuments),
      ) as Prisma.InputJsonValue,
      dependents: JSON.parse(
        JSON.stringify(employee.dependents),
      ) as Prisma.InputJsonValue,
      status: employee.status,
      createdBy: employee.createdBy,
      updatedBy: employee.updatedBy,
    };
  }

  async create(employee: Employee): Promise<Employee> {
    const created = await this.prisma.employee.create({
      data: this.toPrisma(employee),
    });

    return this.toDomain(created as PrismaEmployeeWithRelations);
  }

  async findByEmail(email: string): Promise<Employee | null> {
    const employee = await this.prisma.employee.findUnique({
      where: { email },
    });
    if (!employee || employee.deletedAt) return null;
    return this.toDomain(employee as PrismaEmployeeWithRelations);
  }

  async findById(id: number): Promise<Employee | null> {
    const employee = await this.prisma.employee.findUnique({
      where: { id },
    });
    if (!employee || employee.deletedAt) return null;
    return this.toDomain(employee as PrismaEmployeeWithRelations);
  }

  async findByUuid(uuid: string): Promise<Employee | null> {
    const employee = await this.prisma.employee.findUnique({
      where: { uuid },
    });
    if (!employee || employee.deletedAt) return null;
    return this.toDomain(employee as PrismaEmployeeWithRelations);
  }

  async findAll(): Promise<Employee[]> {
    const employees = await this.prisma.employee.findMany({
      where: { deletedAt: null },
    });
    return employees.map((e) =>
      this.toDomain(e as PrismaEmployeeWithRelations),
    );
  }

  async findWithPagination(params: {
    limit: number;
    offset: number;
    name?: string;
    email?: string;
  }): Promise<{ items: Employee[]; total: number }> {
    const where: Prisma.EmployeeWhereInput = { deletedAt: null };
    if (params.name) {
      where.name = {
        contains: params.name,
        mode: 'insensitive',
      };
    }
    if (params.email) {
      where.email = {
        contains: params.email,
        mode: 'insensitive',
      };
    }
    const [items, total] = await Promise.all([
      this.prisma.employee.findMany({
        where,
        skip: params.offset,
        take: params.limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.employee.count({ where }),
    ]);
    return {
      items: items.map((employee) =>
        this.toDomain(employee as PrismaEmployeeWithRelations),
      ),
      total,
    };
  }

  async update(employee: Employee): Promise<Employee> {
    const updated = await this.prisma.employee.update({
      where: { uuid: employee.uuid },
      data: {
        name: employee.name,
        email: employee.email,
        position: employee.position,
        department: employee.department,
        hireDate: new Date(employee.hireDate),
        address: JSON.parse(
          JSON.stringify(employee.address),
        ) as Prisma.InputJsonValue,
        personalDocuments: JSON.parse(
          JSON.stringify(employee.personalDocuments),
        ) as Prisma.InputJsonValue,
        dependents: JSON.parse(
          JSON.stringify(employee.dependents),
        ) as Prisma.InputJsonValue,
        status: employee.status,
        updatedBy: employee.updatedBy,
      },
    });
    return this.toDomain(updated as PrismaEmployeeWithRelations);
  }

  async delete(uuid: string): Promise<void> {
    await this.prisma.employee.update({
      where: { uuid },
      data: { deletedAt: new Date() },
    });
  }
}
