import { Injectable } from '@nestjs/common';
import { KeycloakService } from './keycloak.service';
import { KeycloakAdminUtils } from './keycloak.admin.utils';
import axios from 'axios';

/**
 * Interface to represent user data for external identity provider
 */
export interface IdentityProviderUserData {
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  password?: string;
  attributes?: Record<string, unknown>;
}

/**
 * Interface for external identity provider service (e.g. Keycloak)
 * This interface belongs to the domain layer and abstracts the concrete implementation
 * in the infrastructure layer, following Clean Architecture principles
 */
export interface IdentityProviderService {
  /**
   * Registers a user with the external identity provider
   * @param userData User data for registration
   * @returns ID of the user in the external provider
   */
  registerUser(userData: IdentityProviderUserData): Promise<string>;

  /**
   * Assigns roles to a user in the external identity provider
   * @param userId ID of the user in the external provider
   * @param roles Array of role names to assign
   */
  assignUserRoles(userId: string, roles: string[]): Promise<void>;

  /**
   * Authenticates a user with the external identity provider
   * @param username Username or email
   * @param password User password
   * @returns Access token
   */
  authenticate(
    username: string,
    password: string,
  ): Promise<{
    access_token: string;
    refresh_token?: string;
    expires_in: number;
  }>;

  /**
   * Refreshes an access token using a refresh token
   * @param refreshToken Refresh token
   * @returns New access token
   */
  refreshToken(refreshToken: string): Promise<{ access_token: string }>;

  /**
   * Invalidates a refresh token
   * @param refreshToken Refresh token to invalidate
   */
  logout(refreshToken: string): Promise<void>;

  /**
   * Gets user information from a token
   * @param token Access token
   * @returns User information
   */
  getUserInfo(token: string): Promise<{
    id: string;
    username: string;
    email: string;
    name?: string;
    roles?: string[];
  }>;
}

/**
 * Concrete implementation of the identity provider service using Keycloak
 * Implements the IdentityProviderService interface from the domain layer
 * following Clean Architecture principles
 */
@Injectable()
export class KeycloakIdentityProviderService
  implements IdentityProviderService
{
  constructor(
    private readonly keycloakService: KeycloakService,
    private readonly keycloakAdminUtils: KeycloakAdminUtils,
  ) {}

  /**
   * Registers a user in Keycloak
   * @param userData User data for registration
   * @returns ID of the user in Keycloak
   */
  async registerUser(userData: IdentityProviderUserData): Promise<string> {
    try {
      // Get admin auth headers with token
      const headers = await this.keycloakAdminUtils.getAdminAuthHeaders();

      // Prepare payload for Keycloak
      const keycloakUser = {
        username: userData.username,
        email: userData.email,
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        enabled: true,
        emailVerified: true,
        credentials: userData.password
          ? [
              {
                type: 'password',
                value: userData.password,
                temporary: false,
              },
            ]
          : undefined,
      };

      // Get the baseUrl and realm from KeycloakAdminUtils
      // Type assertion for accessing private properties safely
      type KeycloakAdmin = {
        baseUrl: string;
        realm: string;
      };
      const adminUtils = this.keycloakAdminUtils as unknown as KeycloakAdmin;
      const baseUrl = adminUtils.baseUrl;
      const realm = adminUtils.realm;

      // Construct URL for Keycloak users API
      const usersUrl = `${baseUrl}/admin/realms/${realm}/users`;

      // Create user in Keycloak
      const response = await axios.post(usersUrl, keycloakUser, { headers });

      // Keycloak returns the created resource URL in the Location header
      // Ex: ".../admin/realms/master/users/UUID"
      const locationHeader = response.headers.location as string;
      if (!locationHeader) {
        throw new Error(
          'User created but location header not returned by Keycloak',
        );
      }

      // Extract user UUID from Location header
      const userId = locationHeader.substring(
        locationHeader.lastIndexOf('/') + 1,
      );

      return userId;
    } catch (error) {
      const errorMsg: string = error.response?.data || error.message || error;
      console.error(
        '[Keycloak] Erro ao registrar usuário:',
        errorMsg,
      );
      throw new Error(
        `Failed to register user in Keycloak: ${
          errorMsg
        }`,
      );
    }
  }

  /**
   * Assigns roles to a user in Keycloak
   * @param userId ID of the user in Keycloak
   * @param roles Array of role names to assign
   */
  async assignUserRoles(userId: string, roles: string[]): Promise<void> {
    try {
      // Get admin auth headers with token
      const headers = await this.keycloakAdminUtils.getAdminAuthHeaders();

      // Get the baseUrl and realm from KeycloakAdminUtils
      // Type assertion for accessing private properties safely
      type KeycloakAdmin = {
        baseUrl: string;
        realm: string;
      };
      const adminUtils = this.keycloakAdminUtils as unknown as KeycloakAdmin;
      const baseUrl = adminUtils.baseUrl;
      const realm = adminUtils.realm;

      // First, get available roles in the realm
      const rolesUrl = `${baseUrl}/admin/realms/${realm}/roles`;
      const rolesResponse = await axios.get(rolesUrl, { headers });

      // Filter only the roles we want to assign that exist in Keycloak
      type KeycloakRole = {
        name: string;
      };
      const availableRoles = rolesResponse.data as Array<KeycloakRole>;
      const rolesToAssign = availableRoles.filter((availableRole) =>
        roles.includes(availableRole.name),
      );

      // LOGS DETALHADOS
      console.log('[Keycloak] assignUserRoles - userId:', userId);
      console.log('[Keycloak] assignUserRoles - roles recebidas:', roles);
      console.log(
        '[Keycloak] assignUserRoles - roles existentes:',
        availableRoles.map((r) => r.name),
      );
      console.log(
        '[Keycloak] assignUserRoles - roles a atribuir:',
        rolesToAssign.map((r) => r.name),
      );

      if (rolesToAssign.length === 0) {
        console.warn(
          `[Keycloak] Nenhuma das roles ${roles.join(', ')} existe em Keycloak`,
        );
        return;
      }

      // Build URL to assign roles to user
      const userRolesUrl = `${baseUrl}/admin/realms/${realm}/users/${userId}/role-mappings/realm`;

      // Log do payload enviado
      console.log(
        '[Keycloak] assignUserRoles - payload enviado:',
        JSON.stringify(rolesToAssign, null, 2),
      );

      // Assign roles to user
      const response = await axios.post(userRolesUrl, rolesToAssign, {
        headers,
      });

      // Log da resposta do Keycloak
      console.log(
        '[Keycloak] assignUserRoles - status resposta:',
        response.status,
      );
      console.log('[Keycloak] assignUserRoles - body resposta:', response.data);
    } catch (error) {
      let errorMsg: unknown = error;
      if (
        typeof error === 'object' &&
        error !== null &&
        'response' in error &&
        typeof (error as { response?: unknown }).response === 'object' &&
        (error as { response?: { data?: unknown } }).response !== null &&
        'data' in (error as { response?: { data?: unknown } }).response!
      ) {
        errorMsg = (error as { response: { data: unknown } }).response.data;
      }
      console.error('[Keycloak] Erro ao atribuir roles ao usuário:', errorMsg);
      throw new Error(
        `Failed to assign roles to user in Keycloak: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /**
   * Authenticates a user with Keycloak
   * @param username Username or email
   * @param password User password
   * @returns Access token and related information
   */
  async authenticate(
    username: string,
    password: string,
  ): Promise<{
    access_token: string;
    refresh_token?: string;
    expires_in: number;
  }> {
    try {
      const response = await this.keycloakService.token(username, password);
      return {
        access_token: response.access_token,
        refresh_token: response.refresh_token,
        expires_in: response.expires_in,
      };
    } catch (error) {
      throw new Error(
        `Keycloak authentication failed: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /**
   * Refreshes an access token using a refresh token
   * @param refreshToken Refresh token
   * @returns New access token
   */
  async refreshToken(refreshToken: string): Promise<{ access_token: string }> {
    try {
      return await this.keycloakService.refreshToken(refreshToken);
    } catch (error) {
      throw new Error(
        `Failed to refresh token in Keycloak: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /**
   * Invalidates a refresh token
   * @param refreshToken Refresh token to invalidate
   */
  async logout(refreshToken: string): Promise<void> {
    try {
      await this.keycloakService.logout(refreshToken);
    } catch (error) {
      throw new Error(
        `Failed to logout in Keycloak: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /**
   * Gets user information from a token
   * @param token Access token
   * @returns User information
   */
  async getUserInfo(token: string): Promise<{
    id: string;
    username: string;
    email: string;
    name?: string;
    roles?: string[];
  }> {
    try {
      const userInfo = await this.keycloakService.getUserInfo(token);
      return {
        id: userInfo.sub,
        username: userInfo.preferred_username,
        email: userInfo.email,
        name: userInfo.name,
        roles: userInfo.roles,
      };
    } catch (error) {
      throw new Error(
        `Failed to get user info from Keycloak: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }
}
