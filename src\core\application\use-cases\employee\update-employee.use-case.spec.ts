import { Test, TestingModule } from '@nestjs/testing';
import { UpdateEmployeeUseCase } from './update-employee.use-case';
import {
  EmployeeRepositoryPort,
  EMPLOYEE_REPOSITORY,
} from '@/core/ports/repositories/employee-repository.port';
import { NotFoundException } from '@nestjs/common';
import { Employee } from '@/core/domain/entities/employee.entity';

// Defina EmployeeStatus localmente se necessário
export enum EmployeeStatusLocal {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

describe('UpdateEmployeeUseCase', () => {
  let useCase: UpdateEmployeeUseCase;
  let repository: jest.Mocked<EmployeeRepositoryPort>;

  const mockWorkSchedule = {
    monday: '08:00-17:00',
    tuesday: '08:00-17:00',
    wednesday: '08:00-17:00',
    thursday: '08:00-17:00',
    friday: '08:00-17:00',
    saturday: '',
    sunday: '',
  };

  const mockVacation = {
    startDate: '2024-07-01',
    endDate: '2024-07-30',
  };

  const mockEmployee = Employee.create(
    1,
    '123e4567-e89b-12d3-a456-426614174000',
    'John Doe',
    '<EMAIL>',
    'Software Engineer',
    'Engineering',
    new Date(),
    {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zipCode: '12345',
      number: '123',
      neighborhood: 'Downtown',
      complement: 'Apt 1',
    },
    [
      {
        type: 'CPF',
        number: '12345678900',
      },
    ],
    [
      {
        name: 'Jane Doe',
        relationship: 'Spouse',
        isTaxDependent: true,
        hasHealthPlan: true,
      },
    ],
    EmployeeStatusLocal.ACTIVE,
    '11111111-**************-************',
    '11111111-**************-************',
    new Date(),
    new Date(),
    mockWorkSchedule,
    ['day'],
    5000.0,
    500.0,
    300.0,
    'Unimed',
    ['CLT'],
    ['senior'],
    '11999999999',
    new Date('1990-01-01'),
    '08:00-17:00',
    true,
    [mockVacation],
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<EmployeeRepositoryPort> = {
      findByUuid: jest.fn(),
      update: jest.fn(),
      findAll: jest.fn(),
      findByEmail: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      findWithPagination: jest.fn(),
      findById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdateEmployeeUseCase,
        {
          provide: EMPLOYEE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<UpdateEmployeeUseCase>(UpdateEmployeeUseCase);
    repository =
      module.get<jest.Mocked<EmployeeRepositoryPort>>(EMPLOYEE_REPOSITORY);
  });

  describe('execute', () => {
    it('should update employee when user is authorized', async () => {
      repository.findByUuid.mockResolvedValue(mockEmployee);
      repository.update.mockImplementation((employee) =>
        Promise.resolve(employee),
      );

      const result = await useCase.execute('test-id', {
        name: 'New Name',
        email: '<EMAIL>',
        address: {
          street: '456 Elm St',
          city: 'Othertown',
          state: 'NY',
          zipCode: '67890',
          number: '456',
          neighborhood: 'Uptown',
          complement: 'Apt 2',
        },
        personalDocuments: [
          {
            type: 'CPF',
            number: '98765432100',
          },
        ],
        dependents: [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            isTaxDependent: true,
            hasHealthPlan: true,
          },
        ],
        status: EmployeeStatusLocal.ACTIVE,
        updatedBy: 'test-updated-by',
      });

      expect(result.name).toBe('New Name');
      expect(result.email).toBe('<EMAIL>');
      expect(result.address.street).toBe('456 Elm St');
      expect(result.address.city).toBe('Othertown');
      expect(result.address.state).toBe('NY');
      expect(result.address.zipCode).toBe('67890');
      expect(result.address.number).toBe('456');
      expect(result.address.neighborhood).toBe('Uptown');
      expect(result.address.complement).toBe('Apt 2');
      expect(result.personalDocuments[0].type).toBe('CPF');
      expect(result.personalDocuments[0].number).toBe('98765432100');
      expect(result.dependents[0].name).toBe('Jane Doe');
      expect(result.dependents[0].relationship).toBe('Spouse');
      expect(result.dependents[0].isTaxDependent).toBe(true);
      expect(result.dependents[0].hasHealthPlan).toBe(true);
      expect(result.status).toBe(EmployeeStatusLocal.ACTIVE);
    });

    it('should update only provided fields', async () => {
      repository.findByUuid.mockResolvedValue(mockEmployee);
      repository.update.mockImplementation((employee) =>
        Promise.resolve(employee),
      );

      const result = await useCase.execute('test-id', {
        updatedBy: 'test-updated-by',
      });

      expect(result.name).toBe(mockEmployee.name);
      expect(result.email).toBe(mockEmployee.email);
      expect(result.address).toEqual(mockEmployee.address);
      expect(result.personalDocuments).toEqual(mockEmployee.personalDocuments);
      expect(result.dependents).toEqual(mockEmployee.dependents);
      expect(result.status).toBe(mockEmployee.status);
    });

    it('should throw NotFoundException when employee does not exist', async () => {
      repository.findByUuid.mockResolvedValue(null);

      await expect(
        useCase.execute('non-existent-id', {
          name: 'New Name',
          email: '<EMAIL>',
          address: {
            street: '456 Elm St',
            city: 'Othertown',
            state: 'NY',
            zipCode: '67890',
            number: '456',
            neighborhood: 'Uptown',
          },
          personalDocuments: [
            {
              type: 'CPF',
              number: '98765432100',
            },
          ],
          dependents: [
            {
              name: 'Jane Doe',
              relationship: 'Spouse',
              isTaxDependent: true,
              hasHealthPlan: true,
            },
          ],
          status: EmployeeStatusLocal.ACTIVE,
          updatedBy: 'test-updated-by',
        }),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
