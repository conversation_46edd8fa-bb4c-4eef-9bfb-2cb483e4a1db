import { Test, TestingModule } from '@nestjs/testing';
import { SupplierService } from '../../services/supplier.service';
import { CreateSupplierUseCase } from '@/core/application/use-cases/supplier/create-supplier.use-case';
import { ListSupplierByUuidUseCase } from '@/core/application/use-cases/supplier/list-supplier-by-uuid.use-case';
import { ListSuppliersUseCase } from '@/core/application/use-cases/supplier/list-suppliers.use-case';
import { DeleteSupplierUseCase } from '@/core/application/use-cases/supplier/delete-supplier.use-case';
import { UpdateSupplierUseCase } from '@/core/application/use-cases/supplier/update-supplier.use-case';
import { CreateSupplierDto } from '../../dto/create-supplier.dto';
import { UpdateSupplierDto } from '../../dto/update-supplier.dto';
import { Supplier } from '@/core/domain/supplier/entities/supplier.entity';
import { SupplierStatus } from '@/core/domain/supplier/enums/supplier-status.enum';
import { SupplierType } from '@/core/domain/supplier/enums/supplier-type.enum';

describe('SupplierService', () => {
  let service: SupplierService;
  let createSupplierUseCase: CreateSupplierUseCase;
  let listSupplierByUuidUseCase: ListSupplierByUuidUseCase;
  let listSuppliersUseCase: ListSuppliersUseCase;
  let deleteSupplierUseCase: DeleteSupplierUseCase;
  let updateSupplierUseCase: UpdateSupplierUseCase;

  // Mock supplier response data
  const mockSupplierData = {
    id: 'supplier-uuid',
    name: 'Test Supplier',
    cnpj: '**********1234',
    tradeName: 'Test Trade Name',
    address: {
      street: 'Test Street',
      city: 'Test City',
      zipCode: '12345678',
      state: 'TS',
    },
    contact: {
      email: '<EMAIL>',
      phone: '**********',
    },
    status: SupplierStatus.ACTIVE,
    createdAt: '2023-01-01T00:00:00.000Z',
    createdBy: 'user-uuid',
    updatedAt: '2023-01-01T00:00:00.000Z',
    updatedBy: 'user-uuid',
  };

  // Mock the Supplier entity
  const mockSupplier = {
    id: 'supplier-uuid',
    name: 'Test Supplier',
    cnpj: '**********1234',
    tradeName: 'Test Trade Name',
    address: {
      toJSON: () => ({
        street: 'Test Street',
        city: 'Test City',
        zipCode: '12345678',
        state: 'TS',
      }),
    },
    contact: {
      toJSON: () => ({
        email: '<EMAIL>',
        phone: '**********',
      }),
    },
    status: SupplierStatus.ACTIVE,
    createdAt: new Date('2023-01-01'),
    createdBy: 'user-uuid',
    updatedAt: new Date('2023-01-01'),
    updatedBy: 'user-uuid',
    toJSON: () => mockSupplierData,
  } as unknown as Supplier;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SupplierService,
        {
          provide: CreateSupplierUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ListSupplierByUuidUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ListSuppliersUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: DeleteSupplierUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: UpdateSupplierUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SupplierService>(SupplierService);
    createSupplierUseCase = module.get<CreateSupplierUseCase>(
      CreateSupplierUseCase,
    );
    listSupplierByUuidUseCase = module.get<ListSupplierByUuidUseCase>(
      ListSupplierByUuidUseCase,
    );
    listSuppliersUseCase =
      module.get<ListSuppliersUseCase>(ListSuppliersUseCase);
    deleteSupplierUseCase = module.get<DeleteSupplierUseCase>(
      DeleteSupplierUseCase,
    );
    updateSupplierUseCase = module.get<UpdateSupplierUseCase>(
      UpdateSupplierUseCase,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSupplier', () => {
    it('should create a supplier and return it as DTO', async () => {
      const createDto: CreateSupplierDto = {
        name: 'Test Supplier',
        document: '**********1234',
        tradeName: 'Test Trade Name',
        address: {
          street: 'Test Street',
          city: 'Test City',
          zipCode: '12345678',
          state: 'TS',
        },
        contact: {
          email: '<EMAIL>',
          phone: '**********',
        },
        type: SupplierType.BETTING,
        status: SupplierStatus.ACTIVE,
      };
      const userId = 'user-uuid';

      const executeSpy = jest
        .spyOn(createSupplierUseCase, 'execute')
        .mockResolvedValue(mockSupplier);

      const result = await service.createSupplier(createDto, userId);

      expect(executeSpy).toHaveBeenCalledWith(createDto, userId);
      expect(result).toEqual(mockSupplierData);
    });
  });

  describe('listSupplierByUuid', () => {
    it('should return a supplier by uuid', async () => {
      const uuid = 'supplier-uuid';

      const executeSpy = jest
        .spyOn(listSupplierByUuidUseCase, 'execute')
        .mockResolvedValue(mockSupplier);

      const result = await service.listSupplierByUuid(uuid);

      expect(executeSpy).toHaveBeenCalledWith(uuid);
      expect(result).toEqual(mockSupplierData);
    });
  });

  describe('listSuppliers', () => {
    it('should return a paginated list of suppliers', async () => {
      const params = { limit: 10, offset: 0, name: 'Test' };
      const mockResult = {
        items: [mockSupplier],
        total: 1,
      };

      const executeSpy = jest
        .spyOn(listSuppliersUseCase, 'execute')
        .mockResolvedValue(mockResult);

      const result = await service.listSuppliers(params);

      expect(executeSpy).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        name: 'Test',
      });
      expect(result).toEqual({
        items: [mockSupplierData],
        total: 1,
        limit: 10,
        offset: 0,
      });
    });
  });

  describe('deleteSupplier', () => {
    it('should delete a supplier', async () => {
      const uuid = 'supplier-uuid';

      const executeSpy = jest
        .spyOn(deleteSupplierUseCase, 'execute')
        .mockResolvedValue(undefined);

      await service.deleteSupplier(uuid);

      expect(executeSpy).toHaveBeenCalledWith({ uuid });
    });
  });

  describe('updateSupplier', () => {
    it('should update a supplier and return it as DTO', async () => {
      const uuid = 'supplier-uuid';
      const updateDto: UpdateSupplierDto = {
        name: 'Updated Supplier',
        tradeName: 'Updated Trade Name',
        address: {
          street: 'Updated Street',
          city: 'Updated City',
          zipCode: '87654321',
          state: 'US',
        },
        contact: {
          email: '<EMAIL>',
          phone: '0987654321',
        },
      };
      const userId = 'user-uuid';

      // Updated supplier data
      const updatedSupplierData = {
        id: 'supplier-uuid',
        name: 'Updated Supplier',
        cnpj: '**********1234',
        tradeName: 'Updated Trade Name',
        address: {
          street: 'Updated Street',
          city: 'Updated City',
          zipCode: '87654321',
          state: 'US',
        },
        contact: {
          email: '<EMAIL>',
          phone: '0987654321',
        },
        status: SupplierStatus.ACTIVE,
        createdAt: '2023-01-01T00:00:00.000Z',
        createdBy: 'user-uuid',
        updatedAt: '2023-01-02T00:00:00.000Z',
        updatedBy: userId,
      };

      // Create a separate mock for the updated supplier
      const updatedMockSupplier = {
        id: 'supplier-uuid',
        name: 'Updated Supplier',
        cnpj: '**********1234',
        tradeName: 'Updated Trade Name',
        address: {
          toJSON: () => ({
            street: 'Updated Street',
            city: 'Updated City',
            zipCode: '87654321',
            state: 'US',
          }),
        },
        contact: {
          toJSON: () => ({
            email: '<EMAIL>',
            phone: '0987654321',
          }),
        },
        status: SupplierStatus.ACTIVE,
        createdAt: new Date('2023-01-01'),
        createdBy: 'user-uuid',
        updatedAt: new Date('2023-01-02'),
        updatedBy: userId,
        toJSON: () => updatedSupplierData,
      } as unknown as Supplier;

      const executeSpy = jest
        .spyOn(updateSupplierUseCase, 'execute')
        .mockResolvedValue(updatedMockSupplier);

      const result = await service.updateSupplier(uuid, updateDto, userId);

      expect(executeSpy).toHaveBeenCalledWith(uuid, updateDto, userId);
      expect(result).toEqual(updatedSupplierData);
    });
  });
});
