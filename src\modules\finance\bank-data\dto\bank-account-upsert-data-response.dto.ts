import { ApiProperty } from '@nestjs/swagger';

export class BankAccountUpsertDataResponseDto {
  @ApiProperty({
    description: 'ID único dos dados de conta bancária',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  id: string;

  @ApiProperty({
    description: 'Nome do campo que foi atualizado ou "created" para criação',
    example: 'accountHolderName',
  })
  fieldName: string;

  @ApiProperty({
    description: 'Valor antigo do campo',
    example: '<PERSON>',
  })
  oldValue?: string | null;

  @ApiProperty({
    description: 'Novo valor do campo',
    example: '<PERSON>',
  })
  newValue?: string | null;

  @ApiProperty({
    description: 'ID do usuário que realizou a atualização',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  upsertByUserId: string;

  @ApiProperty({
    description: 'Nome do usuário que realizou a atualização',
    example: 'admin',
  })
  upsertByUserName: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}
