import { ApiProperty } from '@nestjs/swagger';
import { BankDataResponseDto } from './bank-data-response.dto';

export class PaginatedBankDataResponseDto {
  @ApiProperty({
    description: 'Array of bank data',
    type: [BankDataResponseDto],
  })
  data: BankDataResponseDto[];

  @ApiProperty({
    description: 'Total number of records',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 5,
  })
  totalPages: number;
}
