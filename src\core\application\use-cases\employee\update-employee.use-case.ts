import {
  Address,
  Dependent,
  Employee,
  PersonalDocument,
} from '@/core/domain/entities/employee.entity';
import { upsertByKey } from '@/core/helpers/upsertArray';
import { EMPLOYEE_REPOSITORY } from '@/core/ports/repositories/employee-repository.port';
import { EmployeeRepositoryPort } from '@/core/ports/repositories/employee-repository.port';
import {
  Injectable,
  Inject,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { EmployeeStatus } from '@prisma/client';

@Injectable()
export class UpdateEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,
  ) {}

  async execute(
    id: string,
    updateData: Partial<{
      name: string;
      email: string;
      position: string;
      department: string;
      hireDate: string;
      address: Partial<Address>;
      personalDocuments: Partial<PersonalDocument>[];
      dependents: Partial<Dependent>[];
      status: EmployeeStatus;
    }> & {
      updatedBy: string;
    },
  ): Promise<Employee> {
    const existingEmployee = await this.employeeRepository.findByUuid(id);

    if (!existingEmployee) {
      throw new NotFoundException('Colaborador não encontrado.');
    }

    const existingEmployeeByEmail =
      updateData.email && updateData.email !== existingEmployee.email
        ? await this.employeeRepository.findByEmail(updateData.email)
        : null;

    if (
      existingEmployeeByEmail &&
      existingEmployeeByEmail.uuid !== existingEmployee.uuid
    ) {
      throw new ConflictException({
        code: 'Email já existente',
        message: 'O email do funcionário já existe',
      });
    }

    const address = {
      street: updateData.address?.street ?? existingEmployee.address.street,
      number: updateData.address?.number ?? existingEmployee.address.number,
      neighborhood:
        updateData.address?.neighborhood ??
        existingEmployee.address.neighborhood,
      complement:
        updateData.address?.complement ?? existingEmployee.address.complement,
      city: updateData.address?.city ?? existingEmployee.address.city,
      state: updateData.address?.state ?? existingEmployee.address.state,
      zipCode: updateData.address?.zipCode ?? existingEmployee.address.zipCode,
    };

    let personalDocuments = existingEmployee.personalDocuments;
    if (updateData.personalDocuments) {
      personalDocuments = upsertByKey(
        existingEmployee.personalDocuments,
        updateData.personalDocuments,
        (doc) => `${doc.type}`,
      ).map((doc) => ({
        type: doc.type ?? '',
        number: doc.number ?? '',
        issueDate: doc.issueDate ?? '',
        issuingAgency: doc.issuingAgency ?? '',
      }));
    }

    let dependents = existingEmployee.dependents;
    if (updateData.dependents) {
      dependents = upsertByKey(
        existingEmployee.dependents,
        updateData.dependents,
        (dep) => `${dep.name}-${dep.birthDate}`,
      ).map((dep) => ({
        name: dep.name ?? '',
        birthDate: dep.birthDate ?? '',
        relationship: dep.relationship ?? '',
        isTaxDependent: dep.isTaxDependent ?? false,
        hasHealthPlan: dep.hasHealthPlan ?? false,
      }));
    }

    const updatedEmployee = Employee.create(
      existingEmployee.id,
      existingEmployee.uuid,
      updateData.name ?? existingEmployee.name,
      updateData.email ?? existingEmployee.email,
      updateData.position ?? existingEmployee.position,
      updateData.department ?? existingEmployee.department,
      updateData.hireDate
        ? new Date(updateData.hireDate)
        : existingEmployee.hireDate,
      address,
      personalDocuments,
      dependents,
      updateData.status ?? existingEmployee.status,
      existingEmployee.createdBy,
      updateData.updatedBy ?? existingEmployee.updatedBy,
      existingEmployee.createdAt,
      new Date(),
      existingEmployee.workSchedule,
      existingEmployee.shift,
      existingEmployee.grossSalary,
      existingEmployee.mealAllowance,
      existingEmployee.transportAllowance,
      existingEmployee.healthPlan,
      existingEmployee.contractType,
      existingEmployee.seniority,
      existingEmployee.phone,
      existingEmployee.birthDate,
      existingEmployee.workHours,
      existingEmployee.overtimeBank,
      existingEmployee.vacations,
    );

    return this.employeeRepository.update(updatedEmployee);
  }
}
