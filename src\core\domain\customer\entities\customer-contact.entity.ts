export class CustomerContact {
  id: number;
  customerId: number;
  contact: string;
  type: string;
  area: string;
  responsible: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;

  constructor(props: Omit<CustomerContact, 'id'> & { id?: string }) {
    this.id = props.id !== undefined ? Number(props.id) : undefined as unknown as number;
    this.customerId = props.customerId;
    this.contact = props.contact;
    this.type = props.type;
    this.area = props.area;
    this.responsible = props.responsible;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
    this.deletedAt = props.deletedAt;
  }
} 