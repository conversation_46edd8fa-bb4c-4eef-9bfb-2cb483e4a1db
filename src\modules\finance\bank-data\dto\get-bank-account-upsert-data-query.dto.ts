import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsOptional,
  IsInt,
  Min,
  Max,
  IsString,
  MaxLength,
} from 'class-validator';

export class GetBankAccountUpsertDataQueryDto {
  @ApiProperty({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Transform(({ value }: { value: string }) => parseInt(value))
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
    default: 10,
  })
  @IsOptional()
  @Transform(({ value }: { value: string }) => parseInt(value))
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit must be at most 100' })
  limit?: number = 10;

  @ApiProperty({
    description:
      'Filter by employee ID (optional - if not provided, returns history for all employees)',
    example: 123,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }: { value: string }) => parseInt(value))
  @IsInt({ message: 'Employee ID must be an integer' })
  employeeId?: number;

  @ApiProperty({
    description: 'Filter by field name that was changed',
    example: 'bankName',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Field name must be a string' })
  @MaxLength(100, { message: 'Field name must be at most 100 characters long' })
  fieldName?: string;
}
