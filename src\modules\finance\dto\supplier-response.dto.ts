import { ApiProperty } from '@nestjs/swagger';
import { SupplierStatus } from '../../../core/domain/supplier/enums/supplier-status.enum';

class AddressResponseDto {
  @ApiProperty({ example: 'Rua A, 123' })
  street: string;

  @ApiProperty({ example: '123', required: false })
  number?: string;

  @ApiProperty({ example: 'Apt 45', required: false })
  complement?: string;

  @ApiProperty({ example: 'Bairro Central', required: false })
  neighborhood?: string;

  @ApiProperty({ example: 'Rio de Janeiro' })
  city: string;

  @ApiProperty({ example: '20000-000' })
  zipCode: string;

  @ApiProperty({ example: 'RJ' })
  state: string;
}

export class ContactResponseDto {
  @ApiProperty({ example: '<EMAIL>' })
  contact: string;

  @ApiProperty({ example: 'email' })
  type: string;

  @ApiProperty({ example: 'Finance' })
  area: string;

  @ApiProperty({ example: '<PERSON>' })
  responsible: string;
}

export class SupplierResponseDto {
  @ApiProperty({ example: '660e8400-e29b-41d4-a716-************' })
  id: string;

  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @ApiProperty({ example: 'BANK' })
  type: string;

  @ApiProperty({ example: 'Fornecedor X' })
  name: string;

  @ApiProperty({ example: '**************' })
  cnpj: string;

  @ApiProperty({ example: 'FornX', required: false })
  tradeName?: string;

  @ApiProperty({ type: AddressResponseDto })
  address: AddressResponseDto;

  @ApiProperty({ enum: SupplierStatus, example: SupplierStatus.ACTIVE })
  status: SupplierStatus;

  @ApiProperty({ example: '2025-04-30T23:00:00Z' })
  createdAt: string;

  @ApiProperty({ example: 'admin-user' })
  createdBy: string;

  @ApiProperty({ example: '2025-04-30T23:00:00Z' })
  updatedAt: string;

  @ApiProperty({ example: 'admin-user' })
  updatedBy: string;
}
