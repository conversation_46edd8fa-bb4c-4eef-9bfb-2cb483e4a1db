import { ApiProperty } from '@nestjs/swagger';

export class BankAccountUpsertDataListItemDto {
  @ApiProperty({
    description: 'ID único do registro de histórico',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  id: string;

  @ApiProperty({
    description: 'ID dos dados bancários relacionados',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  bankDataId: string;

  @ApiProperty({
    description: 'Nome do campo que foi alterado',
    example: 'bankName',
  })
  fieldName: string;

  @ApiProperty({
    description: 'Valor antigo do campo',
    example: 'Banco do Brasil S.A.',
    nullable: true,
  })
  oldValue: string | null;

  @ApiProperty({
    description: 'Novo valor do campo',
    example: 'Itaú Unibanco S.A.',
    nullable: true,
  })
  newValue: string | null;

  @ApiProperty({
    description: 'ID do usuário que realizou a alteração',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
    nullable: true,
  })
  upsertByUserId: string | null;

  @ApiProperty({
    description: 'Nome do usuário que realizou a alteração',
    example: '<EMAIL>',
    nullable: true,
  })
  upsertByUserName: string | null;

  @ApiProperty({
    description: 'ID do funcionário que realizou a alteração',
    example: 123,
    nullable: true,
  })
  upsertByEmployeeId: number | null;

  @ApiProperty({
    description: 'Nome do funcionário que realizou a alteração',
    example: 'João da Silva',
    nullable: true,
  })
  upsertByEmployeeName: string | null;

  @ApiProperty({
    description: 'Data da alteração',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class PaginatedBankAccountUpsertDataResponseDto {
  @ApiProperty({
    description: 'Array de histórico de alterações',
    type: [BankAccountUpsertDataListItemDto],
  })
  data: BankAccountUpsertDataListItemDto[];

  @ApiProperty({
    description: 'Total number of records',
    example: 50,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 5,
  })
  totalPages: number;
}
