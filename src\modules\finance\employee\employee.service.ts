import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { CreateEmployeeUseCase } from '@/core/application/use-cases/employee/create-employee.use-case';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { EmployeeResponseDto } from './dto/employee-response.dto';
import { Employee } from '@/core/domain/entities/employee.entity';
import { ListEmployeeQueryDto } from './dto/list-employee-query.dto';
import { PaginatedEmployeeResponseDto } from './dto/paginated-employee-response.dto';
import { ListEmployeeUseCase } from '@/core/application/use-cases/employee/list-employee.use-case';
import { ListEmployeeByUuidUseCase } from '@/core/application/use-cases/employee/list-employee-by-uuid.use-case';
import { DeleteEmployeeUseCase } from '@/core/application/use-cases/employee/delete-employee.use-case';
import { UpdateEmployeeUseCase } from '@/core/application/use-cases/employee/update-employee.use-case';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { calculateWorkJourney } from '@/core/domain/utils/work-journey.util';

@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);

  constructor(
    private readonly createEmployeeUseCase: CreateEmployeeUseCase,
    private readonly listEmployeeUseCase: ListEmployeeUseCase,
    private readonly listEmployeeByUuidUseCase: ListEmployeeByUuidUseCase,
    private readonly updateEmployeeUseCase: UpdateEmployeeUseCase,
    private readonly deleteEmployeeUseCase: DeleteEmployeeUseCase,
  ) {}

  private mapToResponseDto(employee: Employee): EmployeeResponseDto {
    const response = {
      uuid: employee.uuid,
      name: employee.name,
      email: employee.email,
      position: employee.position,
      department: employee.department,
      hireDate: employee.hireDate.toISOString(),
      address: employee.address,
      personalDocuments: employee.personalDocuments,
      dependents: employee.dependents,
      status: employee.status,
      workSchedule: employee.workSchedule,
      shift: employee.shift,
      grossSalary: employee.grossSalary,
      mealAllowance: employee.mealAllowance,
      transportAllowance: employee.transportAllowance,
      healthPlan: employee.healthPlan,
      contractType: employee.contractType,
      seniority: employee.seniority,
      phone: employee.phone,
      birthDate: employee.birthDate?.toISOString(),
      workHours: employee.workHours,
      overtimeBank: employee.overtimeBank,
      vacations: employee.vacations,
      createdBy: employee.createdBy,
      updatedBy: employee.updatedBy,
      createdAt: employee.createdAt.toISOString(),
      updatedAt: employee.updatedAt.toISOString(),
    } as EmployeeResponseDto;

    if (employee.workSchedule) {
      response.workJourney = calculateWorkJourney(employee.workSchedule);
    }

    return response;
  }

  async create(
    createEmployeeDto: CreateEmployeeDto,
  ): Promise<EmployeeResponseDto> {
    const employee =
      await this.createEmployeeUseCase.execute(createEmployeeDto);
    return this.mapToResponseDto(employee);
  }

  async findAll(
    query: ListEmployeeQueryDto,
  ): Promise<PaginatedEmployeeResponseDto> {
    this.logger.log('findAll called with query: ' + JSON.stringify(query));
    try {
      const result = await this.listEmployeeUseCase.execute({
        limit: query.limit ?? 10,
        offset: query.offset ?? 0,
        name: query.name,
        email: query.email,
      });
      this.logger.log('listEmployeeUseCase result: ' + JSON.stringify(result));
      const response = {
        items: result.items.map((employee) => this.mapToResponseDto(employee)),
        total: result.total,
        limit: query.limit ?? 10,
        offset: query.offset ?? 0,
      };
      this.logger.log('Returning response: ' + JSON.stringify(response));
      return response;
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error('Error in findAll:', error.stack || error.message);
        console.error(error);
      } else {
        this.logger.error('Error in findAll:', JSON.stringify(error));
        console.error(error);
      }
      throw error;
    }
  }

  async findByUuid(uuid: string): Promise<EmployeeResponseDto> {
    const employee = await this.listEmployeeByUuidUseCase.execute(uuid);
    if (!employee) {
      throw new NotFoundException('Colaborador não encontrado');
    }
    return this.mapToResponseDto(employee);
  }

  async update(
    uuid: string,
    updateEmployeeDto: UpdateEmployeeDto,
  ): Promise<EmployeeResponseDto> {
    const employee = await this.updateEmployeeUseCase.execute(
      uuid,
      updateEmployeeDto,
    );
    return this.mapToResponseDto(employee);
  }

  async delete(uuid: string): Promise<void> {
    await this.deleteEmployeeUseCase.execute({ uuid });
  }
}
