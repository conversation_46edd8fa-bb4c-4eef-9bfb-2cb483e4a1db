import {
  Injectable,
  Inject,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { SUPPLIER_REPOSITORY } from '../../../ports/repositories/supplier-repository.token';
import { SupplierRepositoryPort } from '../../../ports/repositories/supplier-repository.port';
import { Supplier } from '../../../domain/supplier/entities/supplier.entity';
import { Address } from '../../../domain/supplier/value-objects/address.value-object';
import { Contact } from '../../../domain/supplier/value-objects/contact.value-object';
import { UpdateSupplierDto } from '../../../../modules/finance/dto/update-supplier.dto';

@Injectable()
export class UpdateSupplierUseCase {
  constructor(
    @Inject(SUPPLIER_REPOSITORY)
    private readonly supplierRepository: SupplierRepositoryPort,
  ) { }

  async execute(
    id: string,
    updateData: UpdateSupplierDto,
    userId: string,
  ): Promise<Supplier> {
    const existingSupplier = await this.supplierRepository.findById(id);

    if (!existingSupplier) {
      throw new NotFoundException('Supplier not found');
    }

    const existingSupplierCnpj = updateData.cnpj
      ? await this.supplierRepository.findByDocument(updateData.cnpj)
      : null;

    if (existingSupplierCnpj && existingSupplierCnpj.id !== id) {
      throw new ConflictException('A supplier with this CNPJ already exists');
    }

    const updatedSupplier = new Supplier(
      existingSupplier.id,
      updateData.name ?? existingSupplier.name,
      updateData.cnpj ?? existingSupplier.document,
      updateData.tradeName ?? existingSupplier.tradeName,
      updateData.address
        ? new Address(
          updateData.address.street ?? existingSupplier.address.street,
          updateData.address.number ?? existingSupplier.address.number,
          updateData.address.complement ?? existingSupplier.address.complement,
          updateData.address.neighborhood ?? existingSupplier.address.neighborhood,
          updateData.address.city ?? existingSupplier.address.city,
          updateData.address.zipCode ?? existingSupplier.address.zipCode,
          updateData.address.state ?? existingSupplier.address.state,
        )
        : existingSupplier.address,
      updateData.email ?? existingSupplier.email,
      existingSupplier.classification,
      updateData.type ?? existingSupplier.type,
      updateData.status ?? existingSupplier.status,
      existingSupplier.userId,
      existingSupplier.createdBy,
      existingSupplier.createdAt,
      new Date(),
      userId,
    );

    return this.supplierRepository.update(updatedSupplier);
  }
}
