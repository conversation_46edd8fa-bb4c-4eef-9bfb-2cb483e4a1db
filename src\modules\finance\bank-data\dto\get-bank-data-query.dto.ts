import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsInt, Min, Max, IsEnum } from 'class-validator';
import { BankDataStatus } from '@prisma/client';

export class GetBankDataQueryDto {
  @ApiProperty({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Transform(({ value }: { value: string }) => parseInt(value))
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
    default: 10,
  })
  @IsOptional()
  @Transform(({ value }: { value: string }) => parseInt(value))
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit must be at most 100' })
  limit?: number = 10;

  @ApiProperty({
    description: 'Filter by employee ID',
    example: 123,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }: { value: string }) => parseInt(value))
  @IsInt({ message: 'Employee ID must be an integer' })
  employeeId?: number;

  @ApiProperty({
    description: 'Filter by bank data status',
    enum: BankDataStatus,
    example: BankDataStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(BankDataStatus, {
    message: 'Status must be a valid enum value',
  })
  status?: BankDataStatus;
}
