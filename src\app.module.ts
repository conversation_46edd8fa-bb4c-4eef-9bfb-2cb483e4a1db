import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { ConsoleSpanExporter } from '@opentelemetry/sdk-trace-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
// import { RabbitMQModule } from './infrastructure/messaging/rabbitmq/rabbitmq.module';

// Controladores e serviços
import { createConfig } from './config.module';
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { FinanceModule } from './modules/finance/finance.module';
import { PrismaModule } from './infrastructure/prisma/prisma.module';
import { AllExceptionsFilter } from './infrastructure/exceptions/all-exceptions.filter';
import { AwsModule } from './infrastructure/aws/aws.module';
import { EventsModule } from './infrastructure/events/events.module';
import { HealthModule } from './modules/health/health.module';
import { MetricsModule } from './infrastructure/metrics/metrics.module';
import { MetricsInterceptor } from './infrastructure/metrics/metrics.interceptor';
import { CompanyModule } from './modules/company/company.module';
import { PaymentMethodModule } from './modules/payment-method/payment.method.module';
import { SectorsModule } from './modules/finance/sectors/sectors.module';
import { KeycloakModule } from './infrastructure/keycloak/keycloak.module';
import { CustomersModule } from './modules/customers/customers.module';
import { DocumentModule } from './modules/documents/document.module';
import { LabelsModule } from './modules/labels/labels.module';
import { DomainsModule } from './modules/domains/domains.module';

@Module({
  imports: [
    // Configuração do módulo
    createConfig(),
    // Scheduler para tarefas agendadas
    ScheduleModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    PrismaModule,
    AuthModule,
    UsersModule,
    FinanceModule,
    // RabbitMQModule,
    AwsModule,
    EventsModule,
    HealthModule,
    MetricsModule,
    CompanyModule,
    PaymentMethodModule,
    SectorsModule,
    KeycloakModule,
    CustomersModule,
    DocumentModule,
    LabelsModule,
    DomainsModule,
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: MetricsInterceptor,
    },
  ],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    // Configuração do OpenTelemetry apenas para tracing
    if (process.env.NODE_ENV !== 'test') {
      const sdk = new NodeSDK({
        traceExporter: new ConsoleSpanExporter(),
        instrumentations: [getNodeAutoInstrumentations()],
        serviceName:
          this.configService.get('OTEL_SERVICE_NAME') ||
          'petrus-nest-boilerplate',
      });

      // Iniciar o SDK
      sdk.start();

      // Garantir que o SDK seja encerrado corretamente quando a aplicação for encerrada
      process.on('SIGTERM', () => {
        sdk
          .shutdown()
          .then(() => console.log('SDK encerrado'))
          .catch((error) => console.log('Erro ao encerrar o SDK', error))
          .finally(() => process.exit(0));
      });
    }
  }
}
