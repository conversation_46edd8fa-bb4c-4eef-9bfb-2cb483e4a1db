import { Module } from '@nestjs/common';
import { EmployeeController } from './employee.controller';
import { EmployeeService } from './employee.service';
import { CreateEmployeeUseCase } from '@/core/application/use-cases/employee/create-employee.use-case';
import { PrismaEmployeeRepository } from '@/infrastructure/repositories/prisma-employee.repository';
import { PrismaEventPublisherService } from '@/infrastructure/events/prisma-event-publisher.service';
import { PrismaModule } from '@/infrastructure/prisma/prisma.module';
import { EMPLOYEE_REPOSITORY } from '@/core/ports/repositories/employee-repository.port';
import { ListEmployeeUseCase } from '@/core/application/use-cases/employee/list-employee.use-case';
import { UpdateEmployeeUseCase } from '@/core/application/use-cases/employee/update-employee.use-case';
import { ListEmployeeByUuidUseCase } from '@/core/application/use-cases/employee/list-employee-by-uuid.use-case';
import { DeleteEmployeeUseCase } from '@/core/application/use-cases/employee/delete-employee.use-case';

export const EVENT_PUBLISHER = 'EVENT_PUBLISHER';

@Module({
  imports: [PrismaModule],
  controllers: [EmployeeController],
  providers: [
    CreateEmployeeUseCase,
    ListEmployeeUseCase,
    UpdateEmployeeUseCase,
    ListEmployeeByUuidUseCase,
    DeleteEmployeeUseCase,
    ListEmployeeByUuidUseCase,
    EmployeeService,
    {
      provide: EMPLOYEE_REPOSITORY,
      useClass: PrismaEmployeeRepository,
    },
    {
      provide: EVENT_PUBLISHER,
      useClass: PrismaEventPublisherService,
    },
  ],
  exports: [
    EMPLOYEE_REPOSITORY,
    EVENT_PUBLISHER,
    EmployeeService,
    CreateEmployeeUseCase,
    ListEmployeeUseCase,
    UpdateEmployeeUseCase,
    ListEmployeeByUuidUseCase,
    DeleteEmployeeUseCase,
    ListEmployeeByUuidUseCase,
  ],
})
export class EmployeeModule {}
