name: petrus-backoffice-backend-service-hml
# replicaCount: 1
image:
  repository: 038462752057.dkr.ecr.us-east-2.amazonaws.com/backoffice-backend-service
  tag: latest
  pullPolicy: Always
  containerPort: 3000
environment:
  NODE_ENV: 'production' # From .env
  JWT_SECRET: 'seu_jwt_secret'
  JWT_EXPIRATION: 3600
  RABBITMQ_HOST: b-9029e21f-24d7-4a1b-8266-26f9632cc293.mq.us-east-2.amazonaws.com
  RABBITMQ_PORT: 5671
  RABBITMQ_USERNAME: petrus-mq
  KEYCLOAK_BASE_URL: 'https://iam.hml.ecopagoficial.com'
  KEYCLOAK_REALM: 'cactus-backoffice'
  KEYCLOAK_CLIENT_ID: 'backoffice'
  # KEYCLOAK_CLIENT_ID: backend-dev-client
  # KEYCLOAK_CLIENT_SECRET: myclientsecret
  KEYCLOAK_ADMIN_USERNAME: <EMAIL>
  KEYCLOAK_ADMIN_PASSWORD: admin

  FRONTEND_URL: 'https://cdn-v2.hml.backoffice-cactus.com'

  # AWS
  AWS_REGION: us-east-2
  AWS_S3_BUCKET: petrus-backoffice-hml
  # AWS_S3_BUCKET: s3-petrus-keys
resources:
  requests:
    cpu: 200m
    memory: 200Mi
  limits:
    cpu: 200m
    memory: 200Mi
livenessProbe:
  httpGet:
    path: {}
  initialDelaySeconds: 10
  failureThreshold: 3
  periodSeconds: 10
readinessProbe:
  tcpSocket: null
  initialDelaySeconds: 10
  periodSeconds: 10
  failureThreshold: 3
service:
  type: ClusterIP
  targetPort: 3000
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
  hosts:
    - name: backend.hml.backoffice-cactus.com
      path: /

#################################################################

env:
  secret:
    DATABASE_URL: hml/backoffice-identify-service/DATABASE_URL
    RABBITMQ_PASSWORD: hml/backoffice-identify-service/RABBITMQ_PASSWORD
    KEYCLOAK_CLIENT_SECRET: hml/backoffice-identify-service/KEYCLOAK_CLIENT_SECRET
    AWS_ACCESS_KEY_ID: hml/backoffice-identify-service/AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: hml/backoffice-identify-service/AWS_SECRET_ACCESS_KEY
secretStoreRef:
  name: aws-auth-css
  kind: ClusterSecretStore

hpa:
  enabled: false
  # minReplicas: {}
  # maxReplicas: {}
  # averageCpuUtilization: {}
  # averageMemUtilization: {}
# nodeSelector:
#   project: #ex: assaiclientes / marketplace
