import {
  Body,
  Controller,
  Post,
  UseGuards,
  Get,
  Req,
  UnauthorizedException,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiExcludeEndpoint,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { Request } from 'express';
import { UserProfileDto } from './dto/user-profile.dto';
import { LogoutDto } from './dto/logout.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import {
  ApiAuthLogin,
  ApiAuthRegister,
  ApiAuthRefresh,
  ApiAuthValidate,
  ApiAuthProfile,
  ApiAuthLogout,
  ApiAuthForgotPassword,
} from '../../infrastructure/swagger/decorators/auth.swagger';

interface AuthenticatedRequest extends Request {
  user: {
    sub: string;
    email: string;
    role: string;
  };
}

interface UserFromToken {
  sub?: string;
  id?: string;
  preferred_username?: string;
  username?: string;
  email?: string;
  name?: string;
  realm_access?: { roles: string[] };
}

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('login')
  @HttpCode(200)
  @ApiAuthLogin()
  async token(@Body() loginDto: LoginDto) {
    return this.authService.token(loginDto);
  }
  // Comentado para não ser usado no momento vamos fazer por USER -> Create
  @Post('register')
  @ApiExcludeEndpoint()
  @ApiAuthRegister()
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('refresh')
  @ApiAuthRefresh()
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshToken(refreshTokenDto);
  }

  @Get('validate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiAuthValidate()
  async validate(@Req() req: AuthenticatedRequest) {
    // O JwtAuthGuard já valida o token e adiciona o usuário ao request
    // Adicionando uma promessa que se resolve imediatamente
    await Promise.resolve();
    return {
      message: 'Token válido',
      user: req.user,
    };
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiAuthProfile()
  async getProfile(@Req() req: Request & { user: UserFromToken }): Promise<UserProfileDto> {
    if (!req.user) {
      throw new UnauthorizedException('Usuário não autenticado');
    }
    return this.authService.getUserProfile({
      sub: req.user.sub || '',
      email: req.user.email || req.user.username || '',
      role: req.user.realm_access?.roles?.[0] || 'USER',
    });
  }

  // Comentado para não ser usado no momento
  @Post('logout')
  @HttpCode(204)
  @ApiExcludeEndpoint()
  @ApiAuthLogout()
  async logout(@Body() logoutDto: LogoutDto): Promise<void> {
    await this.authService.logout(logoutDto);
  }

  @Post('forgot-password')
  @HttpCode(204)
  @ApiAuthForgotPassword()
  async forgotPassword(
    @Body() forgotPasswordDto: ForgotPasswordDto,
  ): Promise<void> {
    await this.authService.forgotPassword(forgotPasswordDto);
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Redefinir senha do usuário' })
  @ApiResponse({
    status: 200,
    description: 'Senha alterada com sucesso',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'string', example: 'Success' },
        message: { type: 'string', example: 'Senha alterada com sucesso' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Requisição inválida',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'string', example: 'BadRequest' },
        message: { type: 'string', example: 'Requisição inválida' },
        details: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Token inválido ou expirado',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'string', example: 'Unauthorized' },
        message: { type: 'string', example: 'Token inválido ou expirado' },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Erro interno do servidor',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'string', example: 'InternalError' },
        message: { type: 'string', example: 'Erro interno do servidor' },
      },
    },
  })
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
  ): Promise<{ code: string; message: string }> {
    await this.authService.resetPassword(resetPasswordDto);
    return {
      code: 'Success',
      message: 'Senha alterada com sucesso',
    };
  }
}
