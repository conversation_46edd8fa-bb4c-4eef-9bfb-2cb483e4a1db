import { Test, TestingModule } from '@nestjs/testing';
import { CreateEmployeeUseCase } from './create-employee.use-case';
import {
  EmployeeRepositoryPort,
  EMPLOYEE_REPOSITORY,
} from '@core/ports/repositories/employee-repository.port';
import { ConflictException } from '@nestjs/common';
import { Employee } from '@/core/domain/entities/employee.entity';
import { CreateEmployeeDto } from '@/modules/finance/employee/dto/create-employee.dto';

// Defina EmployeeStatus localmente se necessário
export enum EmployeeStatusLocal {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

describe('CreateEmployeeUseCase', () => {
  let useCase: CreateEmployeeUseCase;
  let repository: jest.Mocked<EmployeeRepositoryPort>;

  const mockWorkSchedule = {
    monday: '08:00-17:00',
    tuesday: '08:00-17:00',
    wednesday: '08:00-17:00',
    thursday: '08:00-17:00',
    friday: '08:00-17:00',
    saturday: '',
    sunday: '',
  };

  const mockVacation = {
    startDate: '2024-07-01',
    endDate: '2024-07-30',
  };

  const mockEmployee = Employee.create(
    1,
    '123e4567-e89b-12d3-a456-426614174000',
    'John Doe',
    '<EMAIL>',
    'Software Engineer',
    'Engineering',
    new Date(),
    {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zipCode: '12345',
      number: '123',
      neighborhood: 'Downtown',
      complement: 'Apt 1',
    },
    [
      {
        type: 'CPF',
        number: '***********',
      },
    ],
    [
      {
        name: 'Jane Doe',
        relationship: 'Spouse',
        isTaxDependent: true,
        hasHealthPlan: true,
      },
    ],
    EmployeeStatusLocal.ACTIVE,
    '********-**************-************',
    '********-**************-************',
    new Date(),
    new Date(),
    mockWorkSchedule,
    ['day'],
    5000.0,
    500.0,
    300.0,
    'Unimed',
    ['CLT'],
    ['senior'],
    '***********',
    new Date('1990-01-01'),
    '08:00-17:00',
    true,
    [mockVacation],
  );

  beforeEach(async () => {
    const mockRepository: jest.Mocked<EmployeeRepositoryPort> = {
      create: jest.fn(),
      findByEmail: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findByUuid: jest.fn(),
      findAll: jest.fn(),
      findWithPagination: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateEmployeeUseCase,
        {
          provide: EMPLOYEE_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<CreateEmployeeUseCase>(CreateEmployeeUseCase);
    repository =
      module.get<jest.Mocked<EmployeeRepositoryPort>>(EMPLOYEE_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  describe('execute', () => {
    const createEmployeeDto: CreateEmployeeDto = {
      name: 'John Doe',
      email: '<EMAIL>',
      position: 'Software Engineer',
      department: 'Engineering',
      hireDate: new Date().toISOString(),
      address: {
        street: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        zipCode: '12345',
        number: '123',
        neighborhood: 'Downtown',
        complement: 'Apt 1',
      },
      personalDocuments: [
        {
          type: 'CPF',
          number: '***********',
        },
      ],
      dependents: [
        {
          name: 'Jane Doe',
          relationship: 'Spouse',
          isTaxDependent: true,
          hasHealthPlan: true,
        },
      ],
      status: EmployeeStatusLocal.ACTIVE,
      workSchedule: mockWorkSchedule,
      shift: ['day'],
      grossSalary: 5000.0,
      mealAllowance: 500.0,
      transportAllowance: 300.0,
      healthPlan: 'Unimed',
      contractType: ['CLT'],
      seniority: ['senior'],
      phone: '***********',
      birthDate: new Date('1990-01-01').toISOString(),
      workHours: '08:00-17:00',
      overtimeBank: true,
      vacations: [mockVacation],
      createdBy: '********-**************-************',
      updatedBy: '********-**************-************',
    };

    it('should create a new employee successfully', async () => {
      repository.findByEmail.mockResolvedValue(null);
      repository.create.mockResolvedValue(mockEmployee);

      const result = await useCase.execute(createEmployeeDto);

      expect(result).toEqual(mockEmployee);
      expect(result.workSchedule).toEqual(mockWorkSchedule);
      expect(result.shift).toEqual(['day']);
      expect(result.grossSalary).toBe(5000.0);
      expect(result.mealAllowance).toBe(500.0);
      expect(result.transportAllowance).toBe(300.0);
      expect(result.healthPlan).toBe('Unimed');
      expect(result.contractType).toEqual(['CLT']);
      expect(result.seniority).toEqual(['senior']);
      expect(result.phone).toBe('***********');
      expect(result.birthDate).toEqual(new Date('1990-01-01'));
      expect(result.workHours).toBe('08:00-17:00');
      expect(result.overtimeBank).toBe(true);
      expect(result.vacations).toEqual([mockVacation]);
    });

    it('should throw ConflictException if email already exists', async () => {
      repository.findByEmail.mockResolvedValue(mockEmployee);

      await expect(useCase.execute(createEmployeeDto)).rejects.toThrow(
        ConflictException,
      );
    });

    it('should create employee with optional fields', async () => {
      const dtoWithoutOptionals = {
        ...createEmployeeDto,
        workSchedule: undefined,
        shift: [],
        grossSalary: undefined,
        mealAllowance: undefined,
        transportAllowance: undefined,
        healthPlan: undefined,
        contractType: undefined,
        seniority: undefined,
        phone: undefined,
        birthDate: undefined,
        workHours: undefined,
        overtimeBank: undefined,
        vacations: undefined,
      };

      const employeeWithoutOptionals = Employee.create(
        2,
        'uuid-456',
        'John Doe',
        '<EMAIL>',
        'Software Engineer',
        'Engineering',
        new Date(),
        {
          street: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          zipCode: '12345',
          number: '123',
          neighborhood: 'Downtown',
          complement: 'Apt 1',
        },
        [
          {
            type: 'CPF',
            number: '***********',
          },
        ],
        [
          {
            name: 'Jane Doe',
            relationship: 'Spouse',
            isTaxDependent: true,
            hasHealthPlan: true,
          },
        ],
        EmployeeStatusLocal.ACTIVE,
        '********-**************-************',
        '********-**************-************',
        new Date(),
        new Date(),
        undefined,
        [],
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
      );

      repository.findByEmail.mockResolvedValue(null);
      repository.create.mockResolvedValue(employeeWithoutOptionals);

      const result = await useCase.execute(dtoWithoutOptionals);

      expect(result.workSchedule).toBeUndefined();
      expect(result.shift).toEqual([]);
      expect(result.grossSalary).toBeUndefined();
      expect(result.mealAllowance).toBeUndefined();
      expect(result.transportAllowance).toBeUndefined();
      expect(result.healthPlan).toBeUndefined();
      expect(result.contractType).toBeUndefined();
      expect(result.seniority).toBeUndefined();
      expect(result.phone).toBeUndefined();
      expect(result.birthDate).toBeUndefined();
      expect(result.workHours).toBeUndefined();
      expect(result.overtimeBank).toBeUndefined();
      expect(result.vacations).toBeUndefined();
    });
  });
});
