import { Injectable } from '@nestjs/common';
import { Prisma, DomainType } from '@prisma/client';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { Domain } from '../../domain/entities/domain.entity';
import { IDomainRepository } from '../../domain/repositories/domain.repository.interface';

@Injectable()
export class DomainRepository implements IDomainRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(domain: Domain): Promise<Domain> {
    const data: Prisma.DomainCreateInput = {
      uuid: domain.uuid,
      customer: { connect: { uuid: domain.customerUuid } },
      domain: domain.domain,
      type: domain.type,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt,
      creator: { connect: { id: domain.createdBy } },
      updater: { connect: { id: domain.updatedBy } },
    };
    return this.prisma.domain.create({ data });
  }

  async update(uuid: string, domain: Partial<Domain>): Promise<Domain> {
    const data: Prisma.DomainUpdateInput = {
      domain: domain.domain,
      type: domain.type,
      updatedAt: new Date(),
      updater: domain.updatedBy ? { connect: { id: domain.updatedBy } } : undefined,
    };
    return this.prisma.domain.update({
      where: { uuid },
      data,
    });
  }

  async delete(uuid: string): Promise<void> {
    await this.prisma.domain.update({
      where: { uuid },
      data: {
        deletedAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }

  async findByUuid(uuid: string): Promise<Domain | null> {
    return this.prisma.domain.findFirst({
      where: {
        uuid,
        deletedAt: null,
      },
    });
  }

  async findByCustomerUuid(
    customerUuid: string,
    params: {
      limit?: number;
      offset?: number;
      domain?: string;
      type?: DomainType;
    },
  ): Promise<{ items: Domain[]; total: number }> {
    const where: Prisma.DomainWhereInput = {
      customerUuid,
      deletedAt: null,
      ...(params.domain && { domain: { contains: params.domain } }),
      ...(params.type && { type: params.type }),
    };

    const [items, total] = await Promise.all([
      this.prisma.domain.findMany({
        where,
        skip: params.offset,
        take: params.limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.domain.count({ where }),
    ]);

    return { items, total };
  }

  async findByDomainAndType(
    customerUuid: string,
    domain: string,
    type: DomainType,
  ): Promise<Domain | null> {
    return this.prisma.domain.findFirst({
      where: {
        customerUuid,
        domain,
        type,
        deletedAt: null,
      },
    });
  }
} 