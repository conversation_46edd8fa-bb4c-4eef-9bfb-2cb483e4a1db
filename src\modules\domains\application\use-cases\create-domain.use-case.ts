import { Injectable, Inject, ConflictException, NotFoundException } from '@nestjs/common';
import { Domain } from '../../domain/entities/domain.entity';
import { IDomainRepository } from '../../domain/repositories/domain.repository.interface';
import { CreateDomainDto } from '../../infrastructure/dtos/domain-create.dto';
import { v4 as uuidv4 } from 'uuid';

export interface CreateDomainInput {
  customerUuid: string;
  domain: CreateDomainDto;
  createdBy: string;
}

@Injectable()
export class CreateDomainUseCase {
  constructor(
    @Inject('IDomainRepository')
    private readonly domainRepository: IDomainRepository,
  ) {}

  async execute(input: CreateDomainInput): Promise<Domain> {
    const existingDomain = await this.domainRepository.findByDomainAndType(
      input.customerUuid,
      input.domain.domain,
      input.domain.type,
    );

    if (existingDomain) {
      throw new ConflictException(
        `Domain ${input.domain.domain} of type ${input.domain.type} already exists for this customer`,
      );
    }

    const domain = {
      uuid: uuidv4(),
      customerUuid: input.customerUuid,
      domain: input.domain.domain,
      type: input.domain.type,
      createdBy: input.createdBy,
      updatedBy: input.createdBy,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as Domain;

    const createdDomain = await this.domainRepository.create(domain);

    return createdDomain;
  }
} 