import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { DomainType } from '@prisma/client';

export class CreateDomainDto {
  @ApiProperty({
    description: 'Customer UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  customerUuid: string;

  @ApiProperty({
    description: 'Domain name',
    example: 'example.com',
  })
  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, {
    message: 'Invalid domain format',
  })
  domain: string;

  @ApiProperty({
    description: 'Domain type',
    enum: DomainType,
    example: DomainType.PRIMARY,
  })
  @IsNotEmpty()
  @IsEnum(DomainType)
  type: DomainType;
} 