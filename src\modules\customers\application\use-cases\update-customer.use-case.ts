import {
  Injectable,
  Inject,
  NotFoundException,
} from '@nestjs/common';
import {
  Customer
} from '../../domain/entities/customer.entity';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import { UpdateCustomerDto } from '../../infrastructure/dtos/update-customer.dto';
import { DuplicateEmailError } from '@/infrastructure/exceptions/duplicate-email.error';
import { DuplicateCnpjError } from '@/infrastructure/exceptions/duplicate-cnpj.error';

export interface UpdateCustomerInput {
  uuid: string;
  data: UpdateCustomerDto;
  updatedBy: string;
}

@Injectable()
export class UpdateCustomerUseCase {
  constructor(
    @Inject('ICustomerRepository')
    private readonly customerRepository: ICustomerRepository,
  ) {}

  async execute(input: UpdateCustomerInput) {
    const customer = await this.customerRepository.findByUuid(input.uuid);
    if (!customer) {
      throw new NotFoundException('Cliente não encontrado.');
    }
    
    if (input.data.cnpj && input.data.cnpj !== customer.cnpj) {
      const docExists = await this.customerRepository.findByDocument(
        input.data.cnpj,
      );
      if (docExists) {
        throw new DuplicateCnpjError(input.data.cnpj, 'cliente');
      }
    }

    if (input.data.email && input.data.email !== customer.email) {
      const emailExists = await this.customerRepository.findByEmail(
        input.data.email,
      );
      if (emailExists) {
        throw new DuplicateEmailError(input.data.email, 'cliente');
      }
    }

    const updateData = {
      razaoSocial: input.data.razaoSocial,
      cnpj: input.data.cnpj,
      email: input.data.email,
      phone: input.data.phone,
      address: input.data.address ? { ...input.data.address } : undefined,
      status: input.data.status,
      image: input.data.image,
      type: input.data.type,
      url: input.data.url,
      updatedBy: input.updatedBy,
    } as Customer;
    const updated = await this.customerRepository.update(input.uuid, updateData);
    return updated;
  }
}
