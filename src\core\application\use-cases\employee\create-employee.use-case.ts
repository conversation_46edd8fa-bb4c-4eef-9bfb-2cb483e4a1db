import { Injectable, Inject } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { ConflictException } from '@nestjs/common';
import { EMPLOYEE_REPOSITORY } from '@/core/ports/repositories/employee-repository.port';
import { EmployeeRepositoryPort } from '@/core/ports/repositories/employee-repository.port';
import { Employee, WorkSchedule } from '@/core/domain/entities/employee.entity';
import { CreateEmployeeDto } from '@/modules/finance/employee/dto/create-employee.dto';

@Injectable()
export class CreateEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepositoryPort,
  ) {}
  async execute(dto: CreateEmployeeDto): Promise<Employee> {
    const existingEmployee = await this.employeeRepository.findByEmail(
      dto.email,
    );

    if (existingEmployee) {
      throw new ConflictException({
        code: 'Email já existente',
        message: 'O email do funcionário já existe',
      });
    }

    const employee = Employee.create(
      0,
      uuidv4(),
      dto.name,
      dto.email,
      dto.position,
      dto.department,
      new Date(dto.hireDate),
      dto.address,
      dto.personalDocuments,
      dto.dependents,
      dto.status,
      dto.createdBy,
      dto.updatedBy,
      new Date(),
      new Date(),
      dto.workSchedule as WorkSchedule | undefined,
      dto.shift,
      dto.grossSalary,
      dto.mealAllowance,
      dto.transportAllowance,
      dto.healthPlan,
      dto.contractType,
      dto.seniority,
      dto.phone,
      dto.birthDate ? new Date(dto.birthDate) : undefined,
      dto.workHours,
      dto.overtimeBank,
      dto.vacations,
    );

    return this.employeeRepository.create(employee);
  }
}
