/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { WorkSchedule } from '@/core/domain/entities/employee.entity';

export function calculateWorkJourney(workSchedule: WorkSchedule): string {
  let totalHours = 0;

  Object.entries(workSchedule).forEach(([_day, schedule]) => {
    if (schedule === 'OFF') return;

    const [startTime, endTime] = schedule.split('-') as [string, string];
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    const hours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
    totalHours += hours;
  });

  return `${totalHours} horas semanais`;
}
