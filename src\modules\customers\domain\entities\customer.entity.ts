export enum CustomerStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
}

export enum CustomerType {
  BANK = 'BANK',
  GAME = 'GAME',
  SPORTSBOOK = 'SPORTSBOOK',
  KYC = 'KYC',
  OTHER = 'OTHER',
}

export interface Customer {
  id?: number;
  uuid: string;
  razaoSocial: string;
  cnpj: string;
  email: string;
  phone?: string;
  address?: Record<string, unknown>;
  image?: string;
  status: CustomerStatus;
  userId: string | null;
  type: CustomerType;
  url: string;
  createdBy: string;
  updatedBy: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}
