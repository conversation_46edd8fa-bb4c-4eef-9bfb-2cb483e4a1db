import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import {
  Customer,
  CustomerStatus,
  CustomerType,
} from '../../domain/entities/customer.entity';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import { CustomerFilterCriteria } from '../../domain/use-cases/list-customers.use-case.interface';

@Injectable()
export class CustomerRepository implements ICustomerRepository {
  constructor(private readonly prisma: PrismaService) { }

  async create(customer: Customer): Promise<Customer> {
    const data: Prisma.CustomerCreateInput = {
      uuid: customer.uuid,
      razaoSocial: customer.razaoSocial,
      cnpj: customer.cnpj,
      email: customer.email,
      phone: customer.phone || null,
      address: customer.address as Prisma.JsonObject,
      image: customer.image || null,
      status: customer.status as unknown as CustomerStatus,
      userId: customer.userId,
      type: customer.type || CustomerType.OTHER,
      url: customer.url || '',
      createdBy: customer.createdBy,
      updatedBy: customer.updatedBy,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
    };
    const created = await this.prisma.customer.create({ data });
    return {
      id: created.id,
      uuid: created.uuid,
      razaoSocial: created.razaoSocial,
      cnpj: created.cnpj,
      email: created.email,
      phone: created.phone || undefined,
      address: created.address as Record<string, unknown>,
      image: created.image || undefined,
      status: created.status as unknown as CustomerStatus,
      userId: created.userId,
      type: created.type as CustomerType,
      url: created.url || '',
      createdBy: created.createdBy,
      updatedBy: created.updatedBy,
      createdAt: created.createdAt,
      updatedAt: created.updatedAt,
    };
  }

  async findByUuid(uuid: string): Promise<Customer | null> {
    const customer = await this.prisma.customer.findUnique({
      where: { uuid },
    });

    if (!customer) {
      return null;
    }

    return {
      id: customer.id,
      uuid: customer.uuid,
      razaoSocial: customer.razaoSocial,
      cnpj: customer.cnpj,
      email: customer.email,
      phone: customer.phone || undefined,
      address: customer.address as Record<string, unknown>,
      image: customer.image || undefined,
      status: customer.status as unknown as CustomerStatus,
      userId: customer.userId,
      type: customer.type as CustomerType,
      url: customer.url || '',
      createdBy: customer.createdBy,
      updatedBy: customer.updatedBy,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      deletedAt: customer.deletedAt || undefined,
    };
  }

  async findByDocument(document: string): Promise<Customer | null> {
    const customer = await this.prisma.customer.findFirst({
      where: { cnpj: document, deletedAt: null },
    });

    if (!customer) {
      return null;
    }

    return {
      id: customer.id,
      uuid: customer.uuid,
      razaoSocial: customer.razaoSocial,
      cnpj: customer.cnpj,
      email: customer.email,
      phone: customer.phone || undefined,
      address: customer.address as Record<string, unknown>,
      image: customer.image || undefined,
      status: customer.status as unknown as CustomerStatus,
      userId: customer.userId,
      type: customer.type as CustomerType,
      url: customer.url || '',
      createdBy: customer.createdBy,
      updatedBy: customer.updatedBy,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      deletedAt: customer.deletedAt || undefined,
    };
  }

  async findByEmail(email: string): Promise<Customer | null> {
    const customer = await this.prisma.customer.findFirst({
      where: { email, deletedAt: null },
    });

    if (!customer) {
      return null;
    }

    return {
      id: customer.id,
      uuid: customer.uuid,
      razaoSocial: customer.razaoSocial,
      cnpj: customer.cnpj,
      email: customer.email,
      phone: customer.phone || undefined,
      address: customer.address as Record<string, unknown>,
      image: customer.image || undefined,
      status: customer.status as unknown as CustomerStatus,
      userId: customer.userId,
      type: customer.type as CustomerType,
      url: customer.url || '',
      createdBy: customer.createdBy,
      updatedBy: customer.updatedBy,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      deletedAt: customer.deletedAt || undefined,
    };
  }

  async findByUserId(userId: string): Promise<Customer | null> {
    const customer = await this.prisma.customer.findFirst({
      where: { userId, deletedAt: null },
    });

    if (!customer) {
      return null;
    }

    return {
      id: customer.id,
      uuid: customer.uuid,
      razaoSocial: customer.razaoSocial,
      cnpj: customer.cnpj,
      email: customer.email,
      phone: customer.phone || undefined,
      address: customer.address as Record<string, unknown>,
      image: customer.image || undefined,
      status: customer.status as unknown as CustomerStatus,
      userId: customer.userId,
      type: customer.type as CustomerType,
      url: customer.url || '',
      createdBy: customer.createdBy,
      updatedBy: customer.updatedBy,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      deletedAt: customer.deletedAt || undefined,
    };
  }

  async listCustomers(
    criteria: CustomerFilterCriteria,
    limit: number,
    offset: number,
    type?: CustomerType,
    status?: CustomerStatus,
  ): Promise<{ customers: Customer[]; total: number }> {
    const where: Prisma.CustomerWhereInput = { deletedAt: null };

    if (criteria.corporateName) {
      where.razaoSocial = {
        contains: criteria.corporateName,
        mode: 'insensitive',
      };
    }

    if (criteria.cnpj) {
      where.cnpj = criteria.cnpj;
    }

    if (type) {
      where.type = type;
    }

    if (status) {
      where.status = status;
    }

    const [customers, total] = await Promise.all([
      this.prisma.customer.findMany({
        where,
        skip: offset,
        take: limit,
      }),
      this.prisma.customer.count({ where }),
    ]);

    return {
      customers: customers.map((customer) => ({
        id: customer.id,
        uuid: customer.uuid,
        razaoSocial: customer.razaoSocial,
        cnpj: customer.cnpj,
        email: customer.email,
        phone: customer.phone || undefined,
        address: customer.address as Record<string, unknown>,
        image: customer.image || undefined,
        status: customer.status as unknown as CustomerStatus,
        userId: customer.userId,
        type: customer.type as CustomerType,
        url: customer.url || '',
        createdBy: customer.createdBy,
        updatedBy: customer.updatedBy,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
        deletedAt: customer.deletedAt || undefined,
      })),
      total,
    };
  }

  async delete(uuid: string): Promise<void> {
    await this.prisma.customer.update({
      where: { uuid },
      data: {
        deletedAt: new Date(),
        status: CustomerStatus.INACTIVE,
      },
    });
  }

  async update(uuid: string, data: Partial<Customer>): Promise<Customer> {
    const { address, ...dataWithoutAddress } = data;
    const prismaData: Record<string, unknown> = {
      ...dataWithoutAddress,
      updatedAt: new Date(),
    };
    if (address !== undefined) {
      prismaData.address = address as Prisma.JsonObject;
    }
    const updated = await this.prisma.customer.update({
      where: { uuid },
      data: prismaData,
    });
    return {
      id: updated.id,
      uuid: updated.uuid,
      razaoSocial: updated.razaoSocial,
      cnpj: updated.cnpj,
      email: updated.email,
      phone: updated.phone || undefined,
      address: updated.address as Record<string, unknown>,
      status: updated.status as CustomerStatus,
      userId: updated.userId,
      type: updated.type as CustomerType,
      url: updated.url || '',
      createdBy: updated.createdBy,
      updatedBy: updated.updatedBy,
      createdAt: updated.createdAt,
      updatedAt: updated.updatedAt,
      deletedAt: updated.deletedAt || undefined,
    };
  }
}
