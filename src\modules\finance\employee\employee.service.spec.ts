import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeService } from './employee.service';
import { CreateEmployeeUseCase } from '@/core/application/use-cases/employee/create-employee.use-case';
import {
  Employee,
  Address,
  PersonalDocument,
  Dependent,
  Vacation,
} from '@/core/domain/entities/employee.entity';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { EmployeeResponseDto } from './dto/employee-response.dto';
import { UpdateEmployeeUseCase } from '@/core/application/use-cases/employee/update-employee.use-case';
import { ListEmployeeByUuidUseCase } from '@/core/application/use-cases/employee/list-employee-by-uuid.use-case';
import { DeleteEmployeeUseCase } from '@/core/application/use-cases/employee/delete-employee.use-case';
import { NotFoundException } from '@nestjs/common';
import { ListEmployeeUseCase } from '@/core/application/use-cases/employee/list-employee.use-case';

export enum EmployeeStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

describe('EmployeeService', () => {
  let service: EmployeeService;
  let createEmployeeUseCase: { execute: jest.Mock };
  let updateEmployeeUseCase: { execute: jest.Mock };
  let deleteEmployeeUseCase: { execute: jest.Mock };
  let listEmployeeByUuidUseCase: { execute: jest.Mock };
  let listEmployeeUseCase: { execute: jest.Mock };

  const mockAddress: Address = {
    street: 'Rua Teste',
    number: '123',
    neighborhood: 'Bairro Teste',
    city: 'Cidade Teste',
    state: 'Estado Teste',
    zipCode: '12345-678',
  };

  const mockPersonalDocument: PersonalDocument = {
    type: 'CPF',
    number: '123.456.789-00',
  };

  const mockDependent = {
    name: 'Maria Silva',
    relationship: 'Filho(a)',
    birthDate: '2010-01-01',
    isTaxDependent: true,
    hasHealthPlan: true,
  };

  const mockWorkSchedule = {
    monday: '08:00-17:00',
    tuesday: '08:00-17:00',
    wednesday: '08:00-17:00',
    thursday: '08:00-17:00',
    friday: '08:00-17:00',
    saturday: '',
    sunday: '',
  };

  const mockVacation: Vacation = {
    startDate: '2024-07-01',
    endDate: '2024-07-30',
  };

  const mockEmployee: Employee = Employee.create(
    1,
    'uuid-123',
    'Funcionário Teste',
    '<EMAIL>',
    'Software Engineer',
    'Engineering',
    new Date('2024-01-01T10:00:00Z'),
    mockAddress,
    [mockPersonalDocument],
    [mockDependent],
    EmployeeStatus.ACTIVE,
    'admin',
    'admin',
    new Date('2024-01-01T10:00:00Z'),
    new Date('2024-01-01T10:00:00Z'),
    mockWorkSchedule,
    ['day'],
    5000.0,
    500.0,
    300.0,
    'Unimed',
    ['CLT'],
    ['senior'],
    '11999999999',
    new Date('1990-01-01'),
    '08:00-17:00',
    true,
    [mockVacation],
  );

  const mockCreateEmployeeDto: CreateEmployeeDto = {
    name: mockEmployee.name,
    email: mockEmployee.email,
    position: 'Software Engineer',
    department: 'Engineering',
    hireDate: new Date('2024-01-01T10:00:00Z').toISOString(),
    address: mockEmployee.address,
    personalDocuments: mockEmployee.personalDocuments,
    dependents: mockEmployee.dependents,
    status: mockEmployee.status,
    workSchedule: mockEmployee.workSchedule,
    shift: mockEmployee.shift,
    grossSalary: mockEmployee.grossSalary,
    mealAllowance: mockEmployee.mealAllowance,
    transportAllowance: mockEmployee.transportAllowance,
    healthPlan: mockEmployee.healthPlan,
    contractType: mockEmployee.contractType,
    seniority: mockEmployee.seniority,
    phone: mockEmployee.phone,
    birthDate: mockEmployee.birthDate?.toISOString(),
    workHours: mockEmployee.workHours,
    overtimeBank: mockEmployee.overtimeBank,
    vacations: mockEmployee.vacations,
    createdBy: mockEmployee.createdBy,
    updatedBy: mockEmployee.updatedBy,
  };

  const mockEmployeeResponse: EmployeeResponseDto = {
    uuid: mockEmployee.uuid,
    name: mockEmployee.name,
    email: mockEmployee.email,
    position: 'Software Engineer',
    department: 'Engineering',
    hireDate: new Date('2024-01-01T10:00:00Z').toISOString(),
    address: mockEmployee.address,
    personalDocuments: mockEmployee.personalDocuments,
    dependents: mockEmployee.dependents,
    status: mockEmployee.status,
    workSchedule: mockEmployee.workSchedule,
    shift: mockEmployee.shift,
    grossSalary: mockEmployee.grossSalary,
    mealAllowance: mockEmployee.mealAllowance,
    transportAllowance: mockEmployee.transportAllowance,
    healthPlan: mockEmployee.healthPlan,
    contractType: mockEmployee.contractType,
    seniority: mockEmployee.seniority,
    phone: mockEmployee.phone,
    birthDate: mockEmployee.birthDate?.toISOString(),
    workHours: mockEmployee.workHours,
    overtimeBank: mockEmployee.overtimeBank,
    vacations: mockEmployee.vacations,
    createdBy: mockEmployee.createdBy,
    updatedBy: mockEmployee.updatedBy,
    createdAt: mockEmployee.createdAt.toISOString(),
    updatedAt: mockEmployee.updatedAt.toISOString(),
    workJourney: 'NaN horas semanais',
  };

  beforeEach(async () => {
    createEmployeeUseCase = {
      execute: jest.fn(),
    };

    updateEmployeeUseCase = {
      execute: jest.fn(),
    };

    deleteEmployeeUseCase = {
      execute: jest.fn(),
    };

    listEmployeeByUuidUseCase = {
      execute: jest.fn(),
    };

    listEmployeeUseCase = {
      execute: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeeService,
        {
          provide: CreateEmployeeUseCase,
          useValue: createEmployeeUseCase,
        },
        {
          provide: ListEmployeeUseCase,
          useValue: listEmployeeUseCase,
        },
        {
          provide: ListEmployeeByUuidUseCase,
          useValue: listEmployeeByUuidUseCase,
        },
        {
          provide: UpdateEmployeeUseCase,
          useValue: updateEmployeeUseCase,
        },
        {
          provide: DeleteEmployeeUseCase,
          useValue: deleteEmployeeUseCase,
        },
      ],
    }).compile();

    service = module.get<EmployeeService>(EmployeeService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findByUuid', () => {
    it('should get employee by uuid', async () => {
      listEmployeeByUuidUseCase.execute.mockResolvedValue(mockEmployee);
      const result = await service.findByUuid('uuid-123');

      const mockListEmployeeByUuidUseCase = jest.spyOn(
        listEmployeeByUuidUseCase,
        'execute',
      );

      expect(result).toEqual(mockEmployeeResponse);
      expect(mockListEmployeeByUuidUseCase).toHaveBeenCalledWith('uuid-123');
    });

    it('should throw NotFoundException when employee does not exist', async () => {
      listEmployeeByUuidUseCase.execute.mockResolvedValue(null);
      await expect(service.findByUuid('non-existent-uuid')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('create', () => {
    it('should create an employee', async () => {
      createEmployeeUseCase.execute.mockResolvedValue(mockEmployee);
      const result = await service.create(mockCreateEmployeeDto);

      const createEmployeeSpyFunction = jest.spyOn(
        createEmployeeUseCase,
        'execute',
      );

      expect(result).toEqual(mockEmployeeResponse);
      expect(createEmployeeSpyFunction).toHaveBeenCalledWith(
        mockCreateEmployeeDto,
      );
    });

    it('should handle empty arrays for personalDocuments and dependents', async () => {
      const employeeWithoutArrays = Employee.create(
        2,
        'uuid-456',
        'Funcionário Sem Arrays',
        '<EMAIL>',
        'Software Engineer',
        'Engineering',
        new Date('2024-01-01T10:00:00Z'),
        mockAddress,
        [],
        [],
        EmployeeStatus.ACTIVE,
        'admin',
        'admin',
        new Date('2024-01-01T10:00:00Z'),
        new Date('2024-01-01T10:00:00Z'),
        mockWorkSchedule,
        ['day'],
        5000.0,
        500.0,
        300.0,
        'Unimed',
        ['CLT'],
        ['senior'],
        '11999999999',
        new Date('1990-01-01'),
        '08:00-17:00',
        true,
        [mockVacation],
      );

      jest
        .spyOn(createEmployeeUseCase, 'execute')
        .mockResolvedValue(employeeWithoutArrays);

      const result = await service.create({
        ...mockCreateEmployeeDto,
        personalDocuments: [],
        dependents: [],
      });

      const createEmployeeSpyFunction = jest.spyOn(
        createEmployeeUseCase,
        'execute',
      );

      expect(result.personalDocuments).toEqual([]);
      expect(result.dependents).toEqual([]);
      expect(createEmployeeSpyFunction).toHaveBeenCalledWith({
        ...mockCreateEmployeeDto,
        personalDocuments: [],
        dependents: [],
      });
    });

    it('should handle optional address complement', async () => {
      const addressWithComplement: Address = {
        ...mockAddress,
        complement: 'Apto 123',
      };

      const employeeWithComplement = Employee.create(
        3,
        'uuid-789',
        'Funcionário Com Complemento',
        '<EMAIL>',
        'Software Engineer',
        'Engineering',
        new Date('2024-01-01T10:00:00Z'),
        addressWithComplement,
        [mockPersonalDocument],
        [mockDependent],
        EmployeeStatus.ACTIVE,
        'admin',
        'admin',
        new Date('2024-01-01T10:00:00Z'),
        new Date('2024-01-01T10:00:00Z'),
        mockWorkSchedule,
        ['day'],
        5000.0,
        500.0,
        300.0,
        'Unimed',
        ['CLT'],
        ['senior'],
        '11999999999',
        new Date('1990-01-01'),
        '08:00-17:00',
        true,
        [mockVacation],
      );

      jest
        .spyOn(createEmployeeUseCase, 'execute')
        .mockResolvedValue(employeeWithComplement);

      const result = await service.create({
        ...mockCreateEmployeeDto,
        address: addressWithComplement,
      });

      const createEmployeeSpyFunction = jest.spyOn(
        createEmployeeUseCase,
        'execute',
      );

      expect(result.address.complement).toBe('Apto 123');
      expect(createEmployeeSpyFunction).toHaveBeenCalledWith({
        ...mockCreateEmployeeDto,
        address: addressWithComplement,
      });
    });

    it('should handle multiple personal documents', async () => {
      const multipleDocuments: PersonalDocument[] = [
        mockPersonalDocument,
        {
          type: 'RG',
          number: '12.345.678-9',
          issuingAgency: 'SSP',
          issueDate: '2020-01-01',
        },
      ];

      const employeeWithMultipleDocuments = Employee.create(
        4,
        'uuid-101',
        'Funcionário Com Múltiplos Documentos',
        '<EMAIL>',
        'Software Engineer',
        'Engineering',
        new Date('2024-01-01T10:00:00Z'),
        mockAddress,
        multipleDocuments,
        [mockDependent],
        EmployeeStatus.ACTIVE,
        'admin',
        'admin',
        new Date('2024-01-01T10:00:00Z'),
        new Date('2024-01-01T10:00:00Z'),
        mockWorkSchedule,
        ['day'],
        5000.0,
        500.0,
        300.0,
        'Unimed',
        ['CLT'],
        ['senior'],
        '11999999999',
        new Date('1990-01-01'),
        '08:00-17:00',
        true,
        [mockVacation],
      );

      jest
        .spyOn(createEmployeeUseCase, 'execute')
        .mockResolvedValue(employeeWithMultipleDocuments);

      const result = await service.create({
        ...mockCreateEmployeeDto,
        personalDocuments: multipleDocuments,
      });

      const createEmployeeSpyFunction = jest.spyOn(
        createEmployeeUseCase,
        'execute',
      );

      expect(result.personalDocuments).toHaveLength(2);
      expect(result.personalDocuments[0]).toEqual(mockPersonalDocument);
      expect(result.personalDocuments[1].type).toBe('RG');
      expect(createEmployeeSpyFunction).toHaveBeenCalledWith({
        ...mockCreateEmployeeDto,
        personalDocuments: multipleDocuments,
      });
    });

    it('should handle multiple dependents', async () => {
      const multipleDependents: Dependent[] = [
        mockDependent,
        {
          name: 'Maria Silva',
          relationship: 'Filha',
          birthDate: '2010-01-01',
          isTaxDependent: false,
          hasHealthPlan: false,
        },
      ];

      const employeeWithMultipleDependents = Employee.create(
        5,
        'uuid-102',
        'Funcionário Com Múltiplos Dependentes',
        '<EMAIL>',
        'Software Engineer',
        'Engineering',
        new Date('2024-01-01T10:00:00Z'),
        mockAddress,
        [mockPersonalDocument],
        multipleDependents,
        EmployeeStatus.ACTIVE,
        'admin',
        'admin',
        new Date('2024-01-01T10:00:00Z'),
        new Date('2024-01-01T10:00:00Z'),
        mockWorkSchedule,
        ['day'],
        5000.0,
        500.0,
        300.0,
        'Unimed',
        ['CLT'],
        ['senior'],
        '11999999999',
        new Date('1990-01-01'),
        '08:00-17:00',
        true,
        [mockVacation],
      );

      jest
        .spyOn(createEmployeeUseCase, 'execute')
        .mockResolvedValue(employeeWithMultipleDependents);

      const result = await service.create({
        ...mockCreateEmployeeDto,
        dependents: multipleDependents,
      });

      const createEmployeeSpyFunction = jest.spyOn(
        createEmployeeUseCase,
        'execute',
      );

      expect(result.dependents).toHaveLength(2);
      expect(result.dependents[0]).toEqual(mockDependent);
      expect(result.dependents[1].name).toBe('Maria Silva');
      expect(createEmployeeSpyFunction).toHaveBeenCalledWith({
        ...mockCreateEmployeeDto,
        dependents: multipleDependents,
      });
    });

    it('should handle different employee statuses', async () => {
      const statuses = [EmployeeStatus.ACTIVE, EmployeeStatus.INACTIVE];

      for (const status of statuses) {
        const employeeWithStatus = Employee.create(
          6,
          `uuid-${status.toLowerCase()}`,
          `Funcionário ${status}`,
          `${status.toLowerCase()}@teste.com`,
          'Software Engineer',
          'Engineering',
          new Date('2024-01-01T10:00:00Z'),
          mockAddress,
          [mockPersonalDocument],
          [mockDependent],
          status,
          'admin',
          'admin',
          new Date('2024-01-01T10:00:00Z'),
          new Date('2024-01-01T10:00:00Z'),
          mockWorkSchedule,
          ['day'],
          5000.0,
          500.0,
          300.0,
          'Unimed',
          ['CLT'],
          ['senior'],
          '11999999999',
          new Date('1990-01-01'),
          '08:00-17:00',
          true,
          [mockVacation],
        );

        jest
          .spyOn(createEmployeeUseCase, 'execute')
          .mockResolvedValue(employeeWithStatus);

        const result = await service.create({
          ...mockCreateEmployeeDto,
          status,
        });

        const createEmployeeSpyFunction = jest.spyOn(
          createEmployeeUseCase,
          'execute',
        );

        expect(result.status).toBe(status);
        expect(createEmployeeSpyFunction).toHaveBeenCalledWith({
          ...mockCreateEmployeeDto,
          status,
        });
      }
    });
  });

  describe('update', () => {
    it('should update an employee', async () => {
      updateEmployeeUseCase.execute.mockResolvedValue(mockEmployee);
      const result = await service.update(
        mockEmployee.uuid,
        mockCreateEmployeeDto,
      );

      const updateEmployeeSpyFunction = jest.spyOn(
        updateEmployeeUseCase,
        'execute',
      );

      expect(result).toEqual(mockEmployeeResponse);
      expect(updateEmployeeSpyFunction).toHaveBeenCalledWith(
        mockEmployee.uuid,
        mockCreateEmployeeDto,
      );
    });

    it('should handle empty arrays for personalDocuments and dependents', async () => {
      const employeeWithoutArrays = Employee.create(
        2,
        'uuid-456',
        'Funcionário Sem Arrays',
        '<EMAIL>',
        'Software Engineer',
        'Engineering',
        new Date('2024-01-01T10:00:00Z'),
        mockAddress,
        [],
        [],
        EmployeeStatus.ACTIVE,
        'admin',
        'admin',
        new Date('2024-01-01T10:00:00Z'),
        new Date('2024-01-01T10:00:00Z'),
        mockWorkSchedule,
        ['day'],
        5000.0,
        500.0,
        300.0,
        'Unimed',
        ['CLT'],
        ['senior'],
        '11999999999',
        new Date('1990-01-01'),
        '08:00-17:00',
        true,
        [mockVacation],
      );

      jest
        .spyOn(updateEmployeeUseCase, 'execute')
        .mockResolvedValue(employeeWithoutArrays);

      const result = await service.update(mockEmployee.uuid, {
        ...mockCreateEmployeeDto,
        personalDocuments: [],
        dependents: [],
      });

      const updateEmployeeSpyFunction = jest.spyOn(
        updateEmployeeUseCase,
        'execute',
      );

      expect(result.personalDocuments).toEqual([]);
      expect(result.dependents).toEqual([]);
      expect(updateEmployeeSpyFunction).toHaveBeenCalledWith(
        mockEmployee.uuid,
        {
          ...mockCreateEmployeeDto,
          personalDocuments: [],
          dependents: [],
        },
      );
    });
  });

  describe('delete', () => {
    it('should delete an employee', async () => {
      await service.delete(mockEmployee.uuid);

      const deleteEmployeeSpyFunction = jest.spyOn(
        deleteEmployeeUseCase,
        'execute',
      );

      expect(deleteEmployeeSpyFunction).toHaveBeenCalledWith({
        uuid: mockEmployee.uuid,
      });
    });

    it('should throw an error when the employee is not found', async () => {
      jest
        .spyOn(deleteEmployeeUseCase, 'execute')
        .mockRejectedValue(new NotFoundException('Colaborador não encontrado'));

      await expect(service.delete(mockEmployee.uuid)).rejects.toThrow(
        'Colaborador não encontrado',
      );
    });
  });
});
