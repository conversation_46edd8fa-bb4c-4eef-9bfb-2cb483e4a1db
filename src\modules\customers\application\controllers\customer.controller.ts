import {
  Controller,
  Get,
  Param,
  HttpCode,
  HttpStatus,
  Query,
  Post,
  Body,
  Delete,
  Patch,
  UseGuards,
  Request,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { CustomerUuidParamDto } from '../../infrastructure/dtos/customer-uuid.dto';
import { FindCustomerByUuidUseCase } from '../use-cases/find-customer-by-uuid.use-case';
import { ListCustomersUseCase } from '../use-cases/list-customers.use-case';
import {
  CustomerListQueryDto,
  CustomerListResponseDto,
  CustomerResponseDto,
} from '../../infrastructure/dtos/customer-list.dto';
import { CustomerFilterCriteria } from '../../domain/use-cases/list-customers.use-case.interface';
import { Customer } from '../../domain/entities/customer.entity';
import {
  ApiListCustomers,
  ApiGetCustomer,
} from '@/infrastructure/swagger/decorators/customers.swagger';
import { CreateCustomerUseCase } from '../use-cases/create-customer.use-case';
import { CreateCustomerDto } from '../../infrastructure/dtos/customer-create.dto';
import { DeleteCustomerUseCase } from '../use-cases/delete-customer.use-case';
import { UpdateCustomerUseCase } from '../use-cases/update-customer.use-case';
import { UpdateCustomerDto } from '../../infrastructure/dtos/update-customer.dto';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../auth/guards/roles.guard';
import { Roles } from '../../../auth/decorators/roles.decorator';
import { Role } from '../../../../core/domain/role.enum';
import { CreateServiceUseCase } from '../../../../core/application/use-cases/service/create-service.use-case';
import { ListServicesUseCase } from '../../../../core/application/use-cases/service/list-services.use-case';
import { ServiceItemDto } from '../../../finance/dto/create-service.dto';
import { ServiceResponseDto } from '../../../finance/dto/service-response.dto';
import { EntityType } from '../../../../core/domain/service/enums/entity-type.enum';
import { CustomerStatus, CustomerType } from '@prisma/client';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
  };
}

@ApiTags('Customers')
@Controller('core/customers')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CustomerController {
  constructor(
    private readonly createCustomerUseCase: CreateCustomerUseCase,
    private readonly findCustomerByUuidUseCase: FindCustomerByUuidUseCase,
    private readonly listCustomersUseCase: ListCustomersUseCase,
    private readonly deleteCustomerUseCase: DeleteCustomerUseCase,
    private readonly updateCustomerUseCase: UpdateCustomerUseCase,
    private readonly createServiceUseCase: CreateServiceUseCase,
    private readonly listServicesUseCase: ListServicesUseCase,
  ) {}

  @Post()
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Create a new customer' })
  @ApiResponse({
    status: 201,
    description: 'Cliente criado com sucesso.',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Dados inválidos.',
  })
  @ApiResponse({
    status: 409,
    description: 'Já existe um cliente com este CNPJ ou email.',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 409 },
        error: { type: 'string', example: 'Conflict' },
        message: { type: 'string', example: 'Já existe um cliente cadastrado com o CNPJ 12345678000195. Por favor, verifique os dados e tente novamente.' },
        field: { type: 'string', example: 'cnpj' },
        value: { type: 'string', example: '12345678000195' },
        entity: { type: 'string', example: 'cliente' },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Erro interno do servidor.',
  })
  @HttpCode(HttpStatus.CREATED)
  async createCustomer(
    @Body() body: CreateCustomerDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<CustomerResponseDto> {
    const customer = await this.createCustomerUseCase.execute({
      customer: body,
      createdBy: req.user.id,
    });

    return CustomerResponseDto.fromEntity(customer);
  }

  @Get()
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiListCustomers()
  async listCustomers(
    @Query() query: CustomerListQueryDto,
  ): Promise<CustomerListResponseDto> {
    const criteria: CustomerFilterCriteria = {
      corporateName: query.filterCorporateName,
      cnpj: query.filterCNPJ,
      type: query.type,
      status: query.status,
    };

    const result = await this.listCustomersUseCase.execute(
      criteria,
      query.limit ?? 10,
      query.offset ?? 0,
    );

    const mapToResponseDto = (customer: Customer) =>
      CustomerResponseDto.fromEntity(customer);

    return {
      items: result.items.map(mapToResponseDto),
      limit: result.limit,
      offset: result.offset,
      total: result.total,
    };
  }

  @Get(':uuid')
  @Roles(Role.ADMIN, Role.CUSTOMER_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiGetCustomer()
  async findByUuid(@Param() params: CustomerUuidParamDto) {
    const customer = await this.findCustomerByUuidUseCase.execute(params.uuid);
    return CustomerResponseDto.fromEntity(customer);
  }

  @Delete(':uuid')
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete (virtual) customer' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Customer UUID',
    type: String,
  })
  @ApiResponse({
    status: 204,
    description: 'Cliente deletado (virtualmente) com sucesso.',
  })
  @ApiResponse({ status: 404, description: 'Cliente não encontrado.' })
  async deleteCustomer(@Param('uuid') uuid: string): Promise<void> {
    await this.deleteCustomerUseCase.execute(uuid);
  }

  @Patch(':uuid')
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Atualiza um cliente' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'Customer UUID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Cliente atualizado com sucesso.',
    type: CustomerResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Cliente não encontrado.' })
  @ApiResponse({
    status: 409,
    description: 'Já existe um cliente com este CNPJ ou email.',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 409 },
        error: { type: 'string', example: 'Conflict' },
        message: { type: 'string', example: 'Já existe um cliente cadastrado com <NAME_EMAIL>. Por favor, verifique os dados e tente novamente.' },
        field: { type: 'string', example: 'email' },
        value: { type: 'string', example: '<EMAIL>' },
        entity: { type: 'string', example: 'cliente' },
      },
    },
  })
  async updateCustomer(
    @Param('uuid') uuid: string,
    @Body() body: UpdateCustomerDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<CustomerResponseDto> {
    const updatedCustomer = await this.updateCustomerUseCase.execute({
      uuid,
      data: body,
      updatedBy: req.user.id,
    });

    return CustomerResponseDto.fromEntity(updatedCustomer);
  }

  @Post(':uuid/services')
  @Roles(Role.USER, Role.ADMIN)
  @ApiOperation({ summary: 'Criar serviços para customer' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Customer',
    type: String,
  })
  @ApiBody({
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          type: {
            type: 'string',
            description: 'Tipo do serviço',
            example: 'Consultoria',
          },
          rate: {
            type: 'string',
            description: 'Taxa ou valor',
            example: 'R$ 200/hora',
          },
          description: {
            type: 'string',
            description: 'Descrição do serviço',
            example: 'Consultoria em marketing digital',
          },
        },
        required: ['type', 'rate', 'description'],
      },
      example: [
        {
          type: 'Consultoria',
          rate: 'R$ 200/hora',
          description: 'Consultoria em marketing digital',
        },
        {
          type: 'Desenvolvimento Web',
          rate: 'R$ 150/hora',
          description: 'Desenvolvimento de sites e aplicações web',
        },
      ],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Serviços criados com sucesso.',
    type: [ServiceResponseDto],
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos.' })
  @ApiResponse({ status: 404, description: 'Customer não encontrado.' })
  async createCustomerService(
    @Param('uuid') uuid: string,
    @Body() servicesData: ServiceItemDto[],
    @Request() req: AuthenticatedRequest,
  ): Promise<ServiceResponseDto[]> {
    // Verificar se o customer existe antes de criar os services
    const customer = await this.findCustomerByUuidUseCase.execute(uuid);
    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    const createdServices = await this.createServiceUseCase.executeMultiple(
      servicesData,
      EntityType.CLIENT,
      uuid,
      req.user.id,
    );

    return createdServices.map((service) =>
      ServiceResponseDto.fromEntity(service),
    );
  }

  @Get(':uuid/services')
  @Roles(Role.USER, Role.ADMIN, Role.CUSTOMER_VIEWER)
  @ApiOperation({ summary: 'Listar serviços do customer' })
  @ApiParam({
    name: 'uuid',
    required: true,
    description: 'UUID do Customer',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de serviços do customer retornada com sucesso.',
    type: [ServiceResponseDto],
  })
  async listCustomerServices(
    @Param('uuid') uuid: string,
  ): Promise<ServiceResponseDto[]> {
    const result = await this.listServicesUseCase.execute({
      limit: 100,
      offset: 0,
      entityUuid: uuid,
      entityType: EntityType.CLIENT,
    });

    return result.items.map(ServiceResponseDto.fromEntity);
  }
}
