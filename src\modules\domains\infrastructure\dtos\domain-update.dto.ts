import { IsString, IsEnum, IsOptional, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { DomainType } from '@prisma/client';

export class DomainUpdateDto {
  @ApiProperty({
    description: 'Domain name',
    example: 'example.com',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, {
    message: 'Invalid domain format',
  })
  domain?: string;

  @ApiProperty({
    description: 'Domain type',
    enum: DomainType,
    example: DomainType.PRIMARY,
    required: false,
  })
  @IsEnum(DomainType)
  @IsOptional()
  type?: DomainType;
} 