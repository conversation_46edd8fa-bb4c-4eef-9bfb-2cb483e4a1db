import { Readable } from 'stream';

export interface IStorageProvider {
  upload(buffer: Buffer, contentType: string, path: string): Promise<void>;
  getFileStream(path: string): Promise<{
    stream: Readable;
    contentType: string;
    fileName: string;
  }>;
  getFileUrl(path: string, expiresIn?: number): Promise<string>;
  getDownloadUrl(path: string, fileName?: string, expiresIn?: number): Promise<string>;
}
