import * as dotenv from 'dotenv';
dotenv.config({ path: '.env.test' });
import { KeycloakService } from '../../keycloak.service';
import { KeycloakAdminUtils } from '../../keycloak.admin.utils';
import { ConfigService } from '@nestjs/config';
import { RequestUtilsService } from '../../../utils/request.utils.service';

describe('KeycloakService', () => {
  let configService: ConfigService;
  let keycloakAdminUtils: KeycloakAdminUtils;
  let requestUtilsService: RequestUtilsService;
  let service: KeycloakService;

  beforeEach(() => {
    configService = {
      get: (key: string): string | undefined => {
        // Busca as variáveis do ambiente de teste
        switch (key) {
          case 'KEYCLOAK_BASE_URL':
            return process.env.KEYCLOAK_BASE_URL;
          case 'KEYCLOAK_REALM':
            return process.env.KEYCLOAK_REALM;
          case 'KEYCLOAK_CLIENT_ID':
            return process.env.KEYCLOAK_CLIENT_ID;
          case 'KEYCLOAK_CLIENT_SECRET':
            return process.env.KEYCLOAK_CLIENT_SECRET;
          default:
            return undefined;
        }
      },
    } as unknown as ConfigService;
    keycloakAdminUtils = {
      getAdminToken: jest.fn().mockResolvedValue('token'),
      createUser: jest.fn().mockResolvedValue('userId'),
      assignRole: jest.fn(),
      removeRole: jest.fn(),
    } as unknown as KeycloakAdminUtils;
    requestUtilsService = {
      executeWithRetry: jest.fn((fn: () => unknown) => fn()),
    } as unknown as RequestUtilsService;
    service = new KeycloakService(
      {} as Record<string, unknown>,
      keycloakAdminUtils,
      configService,
      requestUtilsService,
    );
  });

  it('should throw if config is missing', () => {
    expect(
      () =>
        new KeycloakService(
          {} as Record<string, unknown>,
          keycloakAdminUtils,
          { get: () => undefined } as unknown as ConfigService,
          requestUtilsService,
        ),
    ).toThrow();
  });

  it('should get token', async () => {
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => ({
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 1000,
        token_type: 'bearer',
      }),
    );
    const result = await service.token('user', 'pass');
    expect(result.access_token).toBe('token');
  });

  it('should handle token error', async () => {
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => {
        throw new Error('fail');
      },
    );
    await expect(service.token('user', 'pass')).rejects.toThrow();
  });

  it('should get client credentials token', async () => {
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => ({
        access_token: 'token',
        expires_in: 1000,
        token_type: 'bearer',
      }),
    );
    const result = await service.clientCredentialsToken();
    expect(result.access_token).toBe('token');
  });

  it('should handle client credentials error', async () => {
    (requestUtilsService.executeWithRetry as jest.Mock).mockImplementationOnce(
      () => {
        throw new Error('fail');
      },
    );
    await expect(service.clientCredentialsToken()).rejects.toThrow();
  });

  it('should create user', async () => {
    const result = await service.createUser({});
    expect(result).toBe('userId');
  });

  it('should assign role', async () => {
    await service.assignRole('id', 'role');
    const assignRoleMock = jest.spyOn(keycloakAdminUtils, 'assignRole');
    expect(assignRoleMock).toBeCalled();
  });

  it('should remove role', async () => {
    await service.removeRole('id', 'role');
    const removeRoleMock = jest.spyOn(keycloakAdminUtils, 'removeRole');
    expect(removeRoleMock).toBeCalled();
  });
});
