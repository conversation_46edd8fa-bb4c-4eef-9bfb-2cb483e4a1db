import { CustomerStatus, CustomerType } from '@prisma/client';
import { Customer } from '../entities/customer.entity';
import { CustomerFilterCriteria } from '../use-cases/list-customers.use-case.interface';

export interface ICustomerRepository {
  create(customer: Customer): Promise<Customer>;
  findByUuid(uuid: string): Promise<Customer | null>;
  findByDocument(document: string): Promise<Customer | null>;
  findByEmail(email: string): Promise<Customer | null>;
  findByUserId(userId: string): Promise<Customer | null>;
  listCustomers(
    criteria: CustomerFilterCriteria,
    limit: number,
    offset: number,
    type?: CustomerType,
    status?: CustomerStatus,
  ): Promise<{ customers: Customer[]; total: number }>;
  delete(uuid: string): Promise<void>;
  update(uuid: string, data: Partial<Customer>): Promise<Customer>;
}
